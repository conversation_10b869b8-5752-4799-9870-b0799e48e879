#!/bin/bash

# Build script for Animus Web Application
# This script builds the Docker image and optionally runs it

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="animus-web"
TAG="latest"
RUN_CONTAINER=false
PORT=8080

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -t, --tag TAG       Docker image tag (default: latest)"
    echo "  -r, --run           Run container after building"
    echo "  -p, --port PORT     Port to run container on (default: 8080)"
    echo "  --no-cache          Build without using cache"
    echo ""
    echo "Examples:"
    echo "  $0                  # Build image only"
    echo "  $0 -r               # Build and run container"
    echo "  $0 -t v1.0.0 -r     # Build with tag v1.0.0 and run"
    echo "  $0 --no-cache -r    # Build without cache and run"
}

# Parse command line arguments
DOCKER_BUILD_ARGS=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -r|--run)
            RUN_CONTAINER=true
            shift
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        --no-cache)
            DOCKER_BUILD_ARGS="--no-cache"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

# Build the Docker image
print_status "Building Docker image: ${IMAGE_NAME}:${TAG}"
print_status "Build arguments: ${DOCKER_BUILD_ARGS}"

if docker build ${DOCKER_BUILD_ARGS} -t "${IMAGE_NAME}:${TAG}" .; then
    print_success "Docker image built successfully: ${IMAGE_NAME}:${TAG}"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Show image size
IMAGE_SIZE=$(docker images "${IMAGE_NAME}:${TAG}" --format "table {{.Size}}" | tail -n 1)
print_status "Image size: ${IMAGE_SIZE}"

# Run container if requested
if [ "$RUN_CONTAINER" = true ]; then
    print_status "Starting container on port ${PORT}"
    
    # Stop existing container if running
    if docker ps -q -f name=animus-web-dev | grep -q .; then
        print_warning "Stopping existing container..."
        docker stop animus-web-dev
        docker rm animus-web-dev
    fi
    
    # Run new container
    if docker run -d \
        --name animus-web-dev \
        -p "${PORT}:8080" \
        -v animus_data_dev:/app/data \
        "${IMAGE_NAME}:${TAG}"; then
        
        print_success "Container started successfully"
        print_status "Application will be available at: http://localhost:${PORT}"
        print_status "Official website: http://localhost:${PORT}/"
        print_status "Console: http://localhost:${PORT}/console"
        
        # Wait a moment and check if container is still running
        sleep 3
        if docker ps -q -f name=animus-web-dev | grep -q .; then
            print_success "Container is running healthy"
            
            # Show logs
            print_status "Container logs (last 10 lines):"
            docker logs --tail 10 animus-web-dev
            
            print_status "To view live logs: docker logs -f animus-web-dev"
            print_status "To stop container: docker stop animus-web-dev"
        else
            print_error "Container stopped unexpectedly"
            print_status "Container logs:"
            docker logs animus-web-dev
            exit 1
        fi
    else
        print_error "Failed to start container"
        exit 1
    fi
fi

print_success "Build process completed successfully!"
