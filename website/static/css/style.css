/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 根变量定义 */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --accent-color: #06b6d4;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-light: #94a3b8;
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --border-color: #334155;
    }
}

/* 基础字体和布局 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* 导航栏样式 */
.navbar {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: -0.02em;
    transition: var(--transition);
}

.logo:hover {
    color: var(--primary-dark);
    transform: translateY(-1px);
}

.logo-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.3rem;
    margin-right: 0.75rem;
    box-shadow: 0 3px 12px rgba(37, 99, 235, 0.3);
    transition: var(--transition);
}

.logo:hover .logo-icon {
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 16px rgba(37, 99, 235, 0.4);
}

.logo-text {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
    list-style: none;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 0;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.login-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white !important;
    padding: 0.875rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.login-btn span {
    color: white !important;
}

.login-btn .lang-content {
    color: white !important;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.login-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(37, 99, 235, 0.3);
}

/* 导航栏控制台链接样式 - 与其他导航链接保持一致 */
.nav-console-link {
    color: var(--text-primary) !important;
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
}

.nav-console-link:hover {
    color: var(--primary-color) !important;
}

.nav-console-link span {
    color: inherit !important;
}

/* 语言切换器样式 */
.language-switcher {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: 1rem;
}

.language-toggle {
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.4rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.language-toggle:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--text-primary);
}

.language-toggle.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.language-toggle .flag {
    font-size: 1.1em;
}

.language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    min-width: 120px;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.language-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.language-option:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.language-option.active {
    background: var(--primary-color);
    color: white;
}

.language-option .flag {
    font-size: 1.1em;
}

/* 隐藏非当前语言的内容 */
.lang-content {
    display: none !important;
}

.lang-content.active {
    display: block !important;
}

/* 确保联系我们部分的卡片内容正确显示 */
.feature-card .lang-content.active {
    display: block !important;
}

.feature-card .lang-content.active .feature-title,
.feature-card .lang-content.active .feature-description {
    display: block !important;
}

/* 内联语言切换样式 */
.lang-inline {
    display: none;
}

.lang-inline.active {
    display: inline;
}

.lang-flex {
    display: none;
}

.lang-flex.active {
    display: flex;
}

/* 主要内容区域 */
main {
    margin-top: 80px;
}

/* Hero 区域样式 */
.hero {
    background: var(--bg-gradient);
    color: white;
    padding: 6rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-actions {
    text-align: center;
    margin-top: 1.5rem;
}

.cta-button {
    display: inline-block;
    background: white;
    color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* 内容区域样式 */
.section {
    padding: 4rem 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
    line-height: 1.7;
}

/* 特性网格 */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 内容卡片样式 */
.content-card {
    background: var(--bg-primary);
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
}

/* 页脚样式 */
.footer {
    background: var(--bg-secondary);
    padding: 4rem 0 2rem;
    margin-top: 4rem;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.footer-section h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    display: block;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.footer-bottom p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-links {
        gap: 1rem;
    }

    .nav-links a {
        font-size: 0.875rem;
    }

    .language-switcher {
        margin-left: 0.5rem;
    }

    .language-toggle {
        padding: 0.4rem;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .hero-actions {
        margin-top: 2rem;
    }

    .cta-button {
        padding: 0.875rem 1.75rem;
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .container {
        padding: 0 1rem;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .nav-links {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .nav-links li:not(:last-child) {
        display: none;
    }

    .language-toggle span:not(.flag) {
        display: none;
    }

    .language-toggle {
        padding: 0.4rem;
    }

    .hero-actions {
        margin-top: 1.5rem;
    }

    .cta-button {
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer {
        padding: 3rem 0 1.5rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-light);
}
