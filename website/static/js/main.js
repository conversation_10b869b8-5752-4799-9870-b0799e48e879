// 主要的 JavaScript 功能文件

// 语言管理
const LanguageManager = {
    currentLang: 'zh',
    supportedLangs: ['zh', 'en'],

    init() {
        this.loadSavedLanguage();
        this.setupLanguageSwitcher();
        this.updateContent();
    },

    loadSavedLanguage() {
        const saved = localStorage.getItem('animus-language');
        if (saved && this.supportedLangs.includes(saved)) {
            this.currentLang = saved;
        } else {
            // 检测浏览器语言
            const browserLang = navigator.language.toLowerCase();
            if (browserLang.startsWith('en')) {
                this.currentLang = 'en';
            }
        }
    },

    setupLanguageSwitcher() {
        // 这个方法会在导航栏生成后被调用
        this.updateLanguageButton();
    },

    toggleLanguage() {
        // 切换到另一种语言
        const newLang = this.currentLang === 'zh' ? 'en' : 'zh';
        this.switchLanguage(newLang);
    },

    switchLanguage(lang) {
        if (!this.supportedLangs.includes(lang)) return;

        this.currentLang = lang;
        localStorage.setItem('animus-language', lang);
        this.updateContent();
        this.updateLanguageButton();

        // 触发语言切换事件
        const event = new CustomEvent('languageChanged', { detail: { language: lang } });
        document.dispatchEvent(event);
    },

    updateContent() {
        // 更新所有带有 data-lang 属性的元素
        document.querySelectorAll('[data-lang]').forEach(el => {
            const isCurrentLang = el.dataset.lang === this.currentLang;

            if (el.classList.contains('lang-content')) {
                el.style.display = isCurrentLang ? 'block' : 'none';
                el.classList.toggle('active', isCurrentLang);
            } else if (el.classList.contains('lang-inline')) {
                el.style.display = isCurrentLang ? 'inline' : 'none';
                el.classList.toggle('active', isCurrentLang);
            } else if (el.classList.contains('lang-flex')) {
                el.style.display = isCurrentLang ? 'flex' : 'none';
                el.classList.toggle('active', isCurrentLang);
            }
        });

        // 更新页面标题
        this.updatePageTitle();

        // 更新表单占位符等
        this.updateFormElements();
    },

    updateLanguageButton() {
        const toggleBtns = document.querySelectorAll('.language-toggle');
        toggleBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.lang === this.currentLang);
        });
    },

    updatePageTitle() {
        const titleElement = document.querySelector(`title[data-lang="${this.currentLang}"]`);
        if (titleElement) {
            document.title = titleElement.textContent;
        }
    },

    updateFormElements() {
        // 更新占位符
        const placeholders = {
            zh: {
                email: '请输入您的邮箱地址',
                name: '请输入您的姓名',
                company: '请输入公司/组织名称',
                usecase: '请简要描述您的使用场景和需求...',
                message: '请告诉我们更多关于您的需求或问题...'
            },
            en: {
                email: 'Enter your email address',
                name: 'Enter your name',
                company: 'Enter company/organization name',
                usecase: 'Please briefly describe your use case and requirements...',
                message: 'Tell us more about your needs or questions...'
            }
        };

        const currentPlaceholders = placeholders[this.currentLang];
        if (currentPlaceholders) {
            Object.keys(currentPlaceholders).forEach(key => {
                const elements = document.querySelectorAll(`[data-placeholder="${key}"]`);
                elements.forEach(el => {
                    el.placeholder = currentPlaceholders[key];
                });
            });
        }
    },

    getText(key) {
        const texts = {
            zh: {
                submitting: '提交中...',
                subscribing: '订阅中...',
                submitApplication: '提交申请',
                subscribe: '订阅',
                subscribeSuccess: '感谢您的订阅！我们会及时向您发送最新动态。',
                applicationSuccess: '感谢您的申请！我们会在 24 小时内与您联系。',
                requiredFieldsError: '请填写所有必填字段',
                invalidEmailError: '请输入有效的邮箱地址'
            },
            en: {
                submitting: 'Submitting...',
                subscribing: 'Subscribing...',
                submitApplication: 'Submit Application',
                subscribe: 'Subscribe',
                subscribeSuccess: 'Thank you for subscribing! We will send you the latest updates.',
                applicationSuccess: 'Thank you for your application! We will contact you within 24 hours.',
                requiredFieldsError: 'Please fill in all required fields',
                invalidEmailError: 'Please enter a valid email address'
            }
        };

        return texts[this.currentLang]?.[key] || texts.zh[key] || key;
    }
};

// 这个初始化代码已经移动到文件末尾，避免重复初始化

// 初始化动画效果
function initializeAnimations() {
    // 创建 Intersection Observer 用于滚动动画
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // 观察所有需要动画的元素
    const animatedElements = document.querySelectorAll('.feature-card, .content-card, .section-title');
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}

// 初始化导航功能
function initializeNavigation() {
    // 平滑滚动到锚点
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // 导航栏滚动效果
    let lastScrollTop = 0;
    const navbar = document.querySelector('.navbar');

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // 添加/移除背景模糊效果
        if (scrollTop > 100) {
            navbar.style.backdropFilter = 'blur(10px)';
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        } else {
            navbar.style.backdropFilter = 'blur(0px)';
            navbar.style.background = 'rgba(255, 255, 255, 1)';
        }

        lastScrollTop = scrollTop;
    });
}

// 初始化表单功能
function initializeForms() {
    // 邮件订阅表单处理
    const subscribeForm = document.getElementById('subscribeForm');
    if (subscribeForm) {
        subscribeForm.addEventListener('submit', handleSubscribeSubmit);
    }

    // 联系表单处理
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', handleFormSubmit);
    }

    // 表单验证增强
    enhanceFormValidation();
}

// 处理邮件订阅表单提交
function handleSubscribeSubmit(event) {
    event.preventDefault();

    const form = event.target;
    const email = form.querySelector('input[name="email"]').value;
    const submitBtn = form.querySelector('button[type="submit"]');

    // 显示加载状态
    const originalText = submitBtn.textContent;
    submitBtn.textContent = LanguageManager.getText('subscribing');
    submitBtn.disabled = true;

    // 模拟 API 调用
    setTimeout(() => {
        // 显示成功消息
        showNotification(LanguageManager.getText('subscribeSuccess'), 'success');

        // 重置表单
        form.reset();

        // 恢复按钮状态
        submitBtn.textContent = LanguageManager.getText('subscribe');
        submitBtn.disabled = false;

        // 可以在这里添加真实的 API 调用
        console.log('邮件订阅:', { email });
    }, 1500);
}

// 处理联系表单提交
function handleFormSubmit(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');

    // 收集表单数据
    const data = {};
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    // 验证必填字段
    if (!validateContactForm(data)) {
        showNotification(LanguageManager.getText('requiredFieldsError'), 'error');
        return;
    }

    // 显示加载状态
    const originalText = submitBtn.textContent;
    submitBtn.textContent = LanguageManager.getText('submitting');
    submitBtn.disabled = true;

    // 模拟 API 调用
    setTimeout(() => {
        // 显示成功消息
        showNotification(LanguageManager.getText('applicationSuccess'), 'success');

        // 重置表单
        form.reset();

        // 恢复按钮状态
        submitBtn.textContent = LanguageManager.getText('submitApplication');
        submitBtn.disabled = false;

        // 可以在这里添加真实的 API 调用
        console.log('联系表单提交:', data);
    }, 2000);
}

// 验证联系表单
function validateContactForm(data) {
    const required = ['name', 'email', 'interest', 'usecase'];
    return required.every(field => data[field] && data[field].trim());
}

// 增强表单验证
function enhanceFormValidation() {
    // 实时邮箱验证
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !isValidEmail(this.value)) {
                this.setCustomValidity(LanguageManager.getText('invalidEmailError'));
                this.classList.add('error');
            } else {
                this.setCustomValidity('');
                this.classList.remove('error');
            }
        });
    });

    // 字符计数器（对于文本域）
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        const maxLength = textarea.getAttribute('maxlength') || 500;
        const counter = document.createElement('div');
        counter.className = 'char-counter';
        counter.style.cssText = 'font-size: 0.875rem; color: var(--text-light); text-align: right; margin-top: 0.25rem;';

        const updateCounter = () => {
            const remaining = maxLength - textarea.value.length;
            const text = LanguageManager.currentLang === 'zh'
                ? `还可输入 ${remaining} 个字符`
                : `${remaining} characters remaining`;
            counter.textContent = text;

            if (remaining < 50) {
                counter.style.color = 'var(--primary-color)';
            } else {
                counter.style.color = 'var(--text-light)';
            }
        };

        textarea.addEventListener('input', updateCounter);
        textarea.parentNode.insertBefore(counter, textarea.nextSibling);
        updateCounter();
    });
}

// 邮箱验证函数
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 显示通知消息
function showNotification(message, type = 'info') {
    // 移除已存在的通知
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification--${type}`;
    notification.innerHTML = `
        <div class="notification__content">
            <span class="notification__icon">${getNotificationIcon(type)}</span>
            <span class="notification__message">${message}</span>
            <button class="notification__close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        z-index: 10000;
        background: white;
        border-radius: 8px;
        box-shadow: var(--shadow-lg);
        border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        max-width: 400px;
        animation: slideInRight 0.3s ease-out;
    `;

    const contentStyle = `
        padding: 1rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    `;

    const closeStyle = `
        background: none;
        border: none;
        font-size: 1.25rem;
        cursor: pointer;
        color: var(--text-light);
        margin-left: auto;
    `;

    notification.querySelector('.notification__content').style.cssText = contentStyle;
    notification.querySelector('.notification__close').style.cssText = closeStyle;

    // 添加动画样式
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 添加到页面
    document.body.appendChild(notification);

    // 自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// 获取通知图标
function getNotificationIcon(type) {
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };
    return icons[type] || icons.info;
}

// 实用工具函数
const utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 滚动到顶部
    scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            showNotification(
                LanguageManager.currentLang === 'zh' ? '已复制到剪贴板' : 'Copied to clipboard',
                'success'
            );
        } catch (err) {
            showNotification(
                LanguageManager.currentLang === 'zh' ? '复制失败' : 'Copy failed',
                'error'
            );
        }
    }
};

// 导出到全局作用域以供 HTML 调用
window.handleFormSubmit = handleFormSubmit;
window.handleSubscribeSubmit = handleSubscribeSubmit;
window.showNotification = showNotification;
window.utils = utils;
window.LanguageManager = LanguageManager;

// 添加一些有趣的交互效果
function addInteractiveEffects() {
    // 鼠标悬停时的微动画
    const cards = document.querySelectorAll('.feature-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Logo 点击彩蛋
    const logo = document.querySelector('.logo');
    if (logo) {
        let clickCount = 0;
        logo.addEventListener('click', function(e) {
            clickCount++;
            if (clickCount === 5) {
                const message = LanguageManager.currentLang === 'zh'
                    ? '🎉 你发现了一个彩蛋！animus.run 团队向你问好！'
                    : '🎉 You found an easter egg! Greetings from the animus.run team!';
                showNotification(message, 'success');
                clickCount = 0;
            }
        });
    }
}

// 在页面加载完成后添加交互效果
window.addEventListener('load', addInteractiveEffects);

// 监听语言切换事件
document.addEventListener('languageChanged', function(e) {
    // 更新表单按钮文本
    const submitBtn = document.querySelector('#contactForm button[type="submit"]');
    if (submitBtn) {
        submitBtn.textContent = LanguageManager.getText('submitApplication');
    }

    const subscribeBtn = document.querySelector('#subscribeForm button[type="submit"]');
    if (subscribeBtn) {
        subscribeBtn.textContent = LanguageManager.getText('subscribe');
    }

    // 重新初始化字符计数器
    document.querySelectorAll('.char-counter').forEach(counter => {
        counter.remove();
    });
    enhanceFormValidation();
});

// 性能监控（可选）
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('页面加载性能:', {
                'DNS查找': Math.round(perfData.domainLookupEnd - perfData.domainLookupStart),
                'TCP连接': Math.round(perfData.connectEnd - perfData.connectStart),
                '页面加载': Math.round(perfData.loadEventEnd - perfData.navigationStart),
                'DOM解析': Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart)
            });
        }, 0);
    });
}

// 检查登录状态
async function checkLoginStatus() {
    try {
        const response = await fetch('/api/v1/me', {
            method: 'GET',
            credentials: 'include'
        });
        return response.ok;
    } catch (error) {
        console.error('Failed to check login status:', error);
        return false;
    }
}

// 更新导航栏登录状态
async function updateLoginStatus() {
    const isLoggedIn = await checkLoginStatus();

    // 更新控制台链接
    const navConsoleLinks = document.querySelectorAll('.nav-console-link');
    navConsoleLinks.forEach(link => {
        const zhSpan = link.querySelector('[data-lang="zh"]');
        const enSpan = link.querySelector('[data-lang="en"]');

        if (isLoggedIn) {
            if (zhSpan) zhSpan.textContent = '控制台';
            if (enSpan) enSpan.textContent = 'Console';
            link.href = '/console';
        } else {
            if (zhSpan) zhSpan.textContent = '进入控制台';
            if (enSpan) enSpan.textContent = 'Enter Console';
            link.href = '/api/v1/login';
        }
    });

    // 更新Hero区域的CTA按钮
    const ctaButtons = document.querySelectorAll('.cta-button');
    ctaButtons.forEach(button => {
        const zhSpan = button.querySelector('span');
        
        if (isLoggedIn) {
            button.href = '/console';
            // 根据当前语言更新按钮文本
            if (LanguageManager.currentLang === 'zh') {
                if (zhSpan) zhSpan.textContent = '🚀 进入控制台';
            } else {
                if (zhSpan) zhSpan.textContent = '🚀 Enter Console';
            }
        } else {
            button.href = '/api/v1/login';
            // 根据当前语言更新按钮文本
            if (LanguageManager.currentLang === 'zh') {
                if (zhSpan) zhSpan.textContent = '🚀 进入控制台';
            } else {
                if (zhSpan) zhSpan.textContent = '🚀 Enter Console';
            }
        }
    });
}

// 生成导航栏
function generateNavbar() {
    const headerContainer = document.getElementById('header-container');
    if (!headerContainer) return;

    const navbar = `
        <nav class="navbar">
            <div class="container">
                <div class="nav-content">
                    <a href="/" class="logo">
                        <div class="logo-icon">AI</div>
                        <span class="logo-text">Animus</span>
                    </a>
                    <ul class="nav-links">
                        <li>
                            <a href="/" class="lang-content" data-lang="zh">首页</a>
                            <a href="/" class="lang-content" data-lang="en" style="display: none;">Home</a>
                        </li>
                        <li>
                            <a href="#features" class="lang-content" data-lang="zh">功能特性</a>
                            <a href="#features" class="lang-content" data-lang="en" style="display: none;">Features</a>
                        </li>
                        <li>
                            <a href="#use-cases" class="lang-content" data-lang="zh">应用场景</a>
                            <a href="#use-cases" class="lang-content" data-lang="en" style="display: none;">Use Cases</a>
                        </li>
                        <li>
                            <a href="/about" class="lang-content" data-lang="zh">关于我们</a>
                            <a href="/about" class="lang-content" data-lang="en" style="display: none;">About Us</a>
                        </li>
                        <li>
                            <a href="/#contact" class="contact-link lang-content" data-lang="zh">联系我们</a>
                            <a href="/#contact" class="contact-link lang-content" data-lang="en" style="display: none;">Contact</a>
                        </li>
                        <li>
                            <a href="/api/v1/login" class="nav-console-link">
                                <span class="lang-content" data-lang="zh">进入控制台</span>
                                <span class="lang-content" data-lang="en" style="display: none;">Enter Console</span>
                            </a>
                        </li>
                        <li class="language-switcher">
                            <button class="language-toggle" onclick="LanguageManager.toggleLanguage()">
                                <span class="flag lang-content" data-lang="zh">🇨🇳</span>
                                <span class="flag lang-content" data-lang="en" style="display: none;">🇺🇸</span>
                                <span class="lang-text lang-content" data-lang="zh">中文</span>
                                <span class="lang-text lang-content" data-lang="en" style="display: none;">English</span>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    `;

    headerContainer.innerHTML = navbar;
}

// 生成页脚
function generateFooter() {
    const footerContainer = document.getElementById('footer-container');
    if (!footerContainer) return;

    const footer = `
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3>Animus AI</h3>
                        <!-- 中文描述 -->
                        <p class="lang-content active" data-lang="zh">下一代 AI Agent 平台，让人工智能更好地服务于每个人。</p>
                        <!-- 英文描述 -->
                        <p class="lang-content" data-lang="en" style="display: none;">Next-generation AI Agent platform, making artificial intelligence serve everyone better.</p>
                    </div>
                    <div class="footer-section">
                        <!-- 中文产品标题 -->
                        <h3 class="lang-content active" data-lang="zh">产品</h3>
                        <!-- 英文产品标题 -->
                        <h3 class="lang-content" data-lang="en" style="display: none;">Products</h3>

                        <!-- 中文产品链接 -->
                        <div class="lang-content active" data-lang="zh">
                            <a href="#features">功能特性</a>
                            <a href="#use-cases">应用场景</a>
                            <a href="/console">控制台</a>
                        </div>
                        <!-- 英文产品链接 -->
                        <div class="lang-content" data-lang="en" style="display: none;">
                            <a href="#features">Features</a>
                            <a href="#use-cases">Use Cases</a>
                            <a href="/console">Console</a>
                        </div>
                    </div>
                    <div class="footer-section">
                        <!-- 中文支持标题 -->
                        <h3 class="lang-content active" data-lang="zh">支持</h3>
                        <!-- 英文支持标题 -->
                        <h3 class="lang-content" data-lang="en" style="display: none;">Support</h3>

                        <!-- 中文支持链接 -->
                        <div class="lang-content active" data-lang="zh">
                            <a href="#contact">联系我们</a>
                            <a href="/docs">文档</a>
                            <a href="/help">帮助中心</a>
                        </div>
                        <!-- 英文支持链接 -->
                        <div class="lang-content" data-lang="en" style="display: none;">
                            <a href="#contact">Contact Us</a>
                            <a href="/docs">Documentation</a>
                            <a href="/help">Help Center</a>
                        </div>
                    </div>
                    <div class="footer-section">
                        <!-- 中文关于标题 -->
                        <h3 class="lang-content active" data-lang="zh">关于</h3>
                        <!-- 英文关于标题 -->
                        <h3 class="lang-content" data-lang="en" style="display: none;">About</h3>

                        <!-- 中文关于链接 -->
                        <div class="lang-content active" data-lang="zh">
                            <a href="/about">关于我们</a>
                            <a href="/privacy">隐私政策</a>
                            <a href="/terms">服务条款</a>
                        </div>
                        <!-- 英文关于链接 -->
                        <div class="lang-content" data-lang="en" style="display: none;">
                            <a href="/about">About Us</a>
                            <a href="/privacy">Privacy Policy</a>
                            <a href="/terms">Terms of Service</a>
                        </div>
                    </div>
                </div>
                <div class="footer-bottom">
                    <!-- 中文版权信息 -->
                    <p class="lang-content active" data-lang="zh">&copy; 2025 Animus AI. 保留所有权利。浙ICP备2025174705号-1 </p>
                    <!-- 英文版权信息 -->
                    <p class="lang-content" data-lang="en" style="display: none;">&copy; 2025 Animus AI. All rights reserved.</p>
                </div>
            </div>
        </footer>
    `;

    footerContainer.innerHTML = footer;
}

// 平滑滚动到指定元素
function smoothScrollTo(targetId) {
    const target = document.querySelector(targetId);
    if (target) {
        target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 初始化导航链接的平滑滚动
function initializeSmoothScroll() {
    document.addEventListener('click', function(e) {
        const link = e.target.closest('a');
        if (!link) return;

        const href = link.getAttribute('href');

        // 处理同页面锚点链接
        if (href && href.startsWith('#')) {
            e.preventDefault();
            smoothScrollTo(href);
        }
        // 处理跨页面锚点链接 (如 /#contact)
        else if (href && href.startsWith('/#')) {
            // 如果当前不在首页，则跳转到首页并滚动到指定位置
            if (window.location.pathname !== '/') {
                // 让浏览器正常跳转，不阻止默认行为
                return;
            } else {
                // 如果已经在首页，则直接滚动
                e.preventDefault();
                const targetId = href.substring(1); // 移除开头的 /
                smoothScrollTo(targetId);
            }
        }
    });
}

// 检查URL中的锚点并滚动到指定位置
function handlePageAnchor() {
    const hash = window.location.hash;
    if (hash) {
        // 延迟一点时间确保页面完全加载
        setTimeout(() => {
            smoothScrollTo(hash);
        }, 500);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing...');

    // 生成导航栏和页脚
    generateNavbar();
    generateFooter();

    // 初始化语言管理器（在导航栏生成后）
    LanguageManager.init();

    // 强制更新语言内容显示
    setTimeout(() => {
        LanguageManager.updateContent();
    }, 100);

    // 初始化平滑滚动
    initializeSmoothScroll();

    // 处理页面锚点
    handlePageAnchor();

    // 初始化导航
    if (typeof initializeNavigation === 'function') {
        initializeNavigation();
    }

    // 初始化表单验证
    if (typeof initializeForms === 'function') {
        initializeForms();
    }

    // 初始化动画
    if (typeof initializeAnimations === 'function') {
        initializeAnimations();
    }

    // 更新登录状态
    updateLoginStatus();

    console.log('Initialization complete');
});
