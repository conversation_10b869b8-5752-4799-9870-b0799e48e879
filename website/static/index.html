<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- 中文标题 -->
    <title data-lang="zh" class="lang-content active">animus.run - 释放下一代 AI Agent 的潜能</title>
    <!-- 英文标题 -->
    <title data-lang="en" class="lang-content">animus.run - Unleash the Potential of Next-Generation AI Agents</title>

    <meta name="description" content="animus.run 是一个革命性的 AI Agent Sandbox 平台，为开发者提供安全、灵活的云端 AI Agent 开发与运行环境">
    <meta name="keywords" content="AI Agent, AI云电脑, AI沙箱, AI开发平台, 人工智能">
    <link rel="stylesheet" href="/css/style.css">
    <link rel="icon" href="image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚡</text></svg>">
</head>
<body>
    <!-- 页眉容器 -->
    <div id="header-container"></div>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero">
            <div class="container">
                <div class="hero-content fade-in-up">
                    <!-- 中文 Hero -->
                    <div class="lang-content active" data-lang="zh">
                        <h1>释放下一代 AI Agent 的潜能</h1>
                        <p>animus.run 是一个革命性的 AI Agent Sandbox 平台，为开发者和研究者提供安全、灵活、强大的云端 AI Agent 开发与运行环境</p>
                        <div class="hero-actions">
                            <a href="/api/v1/login" class="cta-button">
                                <span>🚀 进入控制台</span>
                            </a>
                        </div>
                    </div>

                    <!-- 英文 Hero -->
                    <div class="lang-content" data-lang="en">
                        <h1>Unleash the Potential of Next-Generation AI Agents</h1>
                        <p>animus.run is a revolutionary AI Agent Sandbox platform that provides developers and researchers with a secure, flexible, and powerful cloud-based environment for AI Agent development and deployment</p>
                        <div class="hero-actions">
                            <a href="/api/v1/login" class="cta-button">
                                <span>🚀 Enter Console</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 简介部分 -->
        <section class="section">
            <div class="container">
                <div class="content-card fade-in-up">
                    <!-- 中文简介 -->
                    <div class="lang-content active" data-lang="zh">
                        <h2 class="section-title">重新定义 AI Agent 开发体验</h2>
                        <p class="section-subtitle">
                            在 AI 技术飞速发展的今天，开发者需要一个既安全又灵活的平台来构建、测试和部署 AI Agent。
                            animus.run 正是为了解决这一挑战而生，我们提供了一个完整的云端 AI Agent 沙箱环境，
                            让您可以专注于创新，而无需担心基础设施的复杂性。
                        </p>
                    </div>

                    <!-- 英文简介 -->
                    <div class="lang-content" data-lang="en">
                        <h2 class="section-title">Redefining the AI Agent Development Experience</h2>
                        <p class="section-subtitle">
                            In today's rapidly evolving AI landscape, developers need a platform that is both secure and flexible
                            for building, testing, and deploying AI Agents. animus.run was born to solve this challenge,
                            providing a complete cloud-based AI Agent sandbox environment that allows you to focus on innovation
                            without worrying about infrastructure complexity.
                        </p>
                    </div>

                    <div class="features-grid">
                        <div class="feature-card">
                            <span class="feature-icon">🔒</span>
                            <!-- 中文特性 -->
                            <div class="lang-content active" data-lang="zh">
                                <h3 class="feature-title">安全隔离环境</h3>
                                <p class="feature-description">
                                    每个 AI Agent 都运行在完全隔离的沙箱环境中，确保数据安全和系统稳定性，
                                    让您可以放心地进行各种实验和开发工作。
                                </p>
                            </div>
                            <!-- 英文特性 -->
                            <div class="lang-content" data-lang="en">
                                <h3 class="feature-title">Secure Isolated Environment</h3>
                                <p class="feature-description">
                                    Each AI Agent runs in a completely isolated sandbox environment, ensuring data security
                                    and system stability, allowing you to conduct various experiments and development work
                                    with confidence.
                                </p>
                            </div>
                        </div>
                        <div class="feature-card">
                            <span class="feature-icon">☁️</span>
                            <!-- 中文特性 -->
                            <div class="lang-content active" data-lang="zh">
                                <h3 class="feature-title">云端原生架构</h3>
                                <p class="feature-description">
                                    基于现代云原生技术构建，提供弹性扩展、高可用性和全球部署能力，
                                    确保您的 AI Agent 能够随时随地稳定运行。
                                </p>
                            </div>
                            <!-- 英文特性 -->
                            <div class="lang-content" data-lang="en">
                                <h3 class="feature-title">Cloud-Native Architecture</h3>
                                <p class="feature-description">
                                    Built on modern cloud-native technologies, providing elastic scaling, high availability,
                                    and global deployment capabilities, ensuring your AI Agents can run stably anytime, anywhere.
                                </p>
                            </div>
                        </div>
                        <div class="feature-card">
                            <span class="feature-icon">🚀</span>
                            <!-- 中文特性 -->
                            <div class="lang-content active" data-lang="zh">
                                <h3 class="feature-title">快速部署与管理</h3>
                                <p class="feature-description">
                                    通过直观的管理界面，您可以轻松创建、配置、监控和管理您的 AI Agent，
                                    从原型到生产环境的部署只需几分钟。
                                </p>
                            </div>
                            <!-- 英文特性 -->
                            <div class="lang-content" data-lang="en">
                                <h3 class="feature-title">Rapid Deployment & Management</h3>
                                <p class="feature-description">
                                    Through an intuitive management interface, you can easily create, configure, monitor,
                                    and manage your AI Agents, with deployment from prototype to production taking just minutes.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 价值主张部分 -->
        <section class="section" style="background: var(--bg-secondary);" id="features">
            <div class="container">
                <!-- 中文标题 -->
                <h2 class="section-title lang-content active" data-lang="zh">为什么选择 animus.run？</h2>
                <!-- 英文标题 -->
                <h2 class="section-title lang-content" data-lang="en">Why Choose animus.run?</h2>

                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🧠</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">AI 优先设计</h3>
                            <p class="feature-description">
                                专为 AI Agent 开发优化，内置常用的机器学习框架、工具链和预配置环境，
                                让您可以立即开始您的 AI 项目。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">AI-First Design</h3>
                            <p class="feature-description">
                                Optimized specifically for AI Agent development, with built-in popular machine learning
                                frameworks, toolchains, and pre-configured environments, allowing you to start your AI
                                projects immediately.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">⚡</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">高性能计算</h3>
                            <p class="feature-description">
                                提供 GPU 加速计算资源，支持大规模模型训练和推理，
                                确保您的 AI Agent 拥有足够的计算能力来处理复杂任务。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">High-Performance Computing</h3>
                            <p class="feature-description">
                                Provides GPU-accelerated computing resources supporting large-scale model training and inference,
                                ensuring your AI Agents have sufficient computational power to handle complex tasks.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🔗</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">丰富的集成能力</h3>
                            <p class="feature-description">
                                支持与主流 AI 服务、数据库、API 的无缝集成，
                                让您的 AI Agent 能够轻松连接和利用外部资源。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Rich Integration Capabilities</h3>
                            <p class="feature-description">
                                Supports seamless integration with mainstream AI services, databases, and APIs,
                                enabling your AI Agents to easily connect and utilize external resources.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📊</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">实时监控与分析</h3>
                            <p class="feature-description">
                                提供详细的性能监控、日志分析和资源使用统计，
                                帮助您深入了解 AI Agent 的运行状态和优化机会。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Real-time Monitoring & Analytics</h3>
                            <p class="feature-description">
                                Provides detailed performance monitoring, log analysis, and resource usage statistics,
                                helping you gain deep insights into AI Agent runtime status and optimization opportunities.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🎯</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">灵活的资源配置</h3>
                            <p class="feature-description">
                                根据您的具体需求动态调整计算资源、存储空间和网络配置，
                                实现成本优化和性能最大化的完美平衡。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Flexible Resource Configuration</h3>
                            <p class="feature-description">
                                Dynamically adjust computing resources, storage space, and network configuration based on
                                your specific needs, achieving the perfect balance of cost optimization and performance maximization.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">👥</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">协作与共享</h3>
                            <p class="feature-description">
                                支持团队协作开发，提供版本控制、权限管理和知识共享功能，
                                让团队能够高效地共同构建复杂的 AI 系统。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Collaboration & Sharing</h3>
                            <p class="feature-description">
                                Supports team collaborative development with version control, permission management,
                                and knowledge sharing features, enabling teams to efficiently build complex AI systems together.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 应用场景部分 -->
        <section class="section" id="use-cases">
            <div class="container">
                <!-- 中文 -->
                <div class="lang-content active" data-lang="zh">
                    <h2 class="section-title">应用场景</h2>
                    <p class="section-subtitle">
                        animus.run 致力于为不同层次的 AI 从业者提供最适合的开发环境
                    </p>
                </div>
                <!-- 英文 -->
                <div class="lang-content" data-lang="en">
                    <h2 class="section-title">Use Cases</h2>
                    <p class="section-subtitle">
                        animus.run is committed to providing the most suitable development environment for AI practitioners at different levels
                    </p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">👨‍💻</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">AI 开发者</h3>
                            <p class="feature-description">
                                为个人开发者和小团队提供快速上手的 AI Agent 开发环境，
                                支持从原型验证到产品化的完整开发流程。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">AI Developers</h3>
                            <p class="feature-description">
                                Provides individual developers and small teams with an easy-to-start AI Agent development environment,
                                supporting the complete development process from prototype validation to productization.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🔬</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">AI 研究人员</h3>
                            <p class="feature-description">
                                为学术研究和实验提供强大的计算资源和灵活的环境配置，
                                支持前沿 AI 算法的研究和验证。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">AI Researchers</h3>
                            <p class="feature-description">
                                Provides powerful computing resources and flexible environment configuration for academic research
                                and experimentation, supporting cutting-edge AI algorithm research and validation.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🏢</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">企业用户</h3>
                            <p class="feature-description">
                                为企业提供安全可靠的 AI Agent 部署和管理平台，
                                支持大规模应用和企业级安全与合规要求。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Enterprise Users</h3>
                            <p class="feature-description">
                                Provides enterprises with a secure and reliable AI Agent deployment and management platform,
                                supporting large-scale applications and enterprise-level security and compliance requirements.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系我们部分 -->
        <section class="section" id="contact">
            <div class="container">
                <!-- 中文 -->
                <div class="lang-content active" data-lang="zh">
                    <h2 class="section-title">联系我们</h2>
                    <p class="section-subtitle">
                        如果您对我们的产品或服务有任何疑问，欢迎随时联系我们
                    </p>
                </div>
                <!-- 英文 -->
                <div class="lang-content" data-lang="en">
                    <h2 class="section-title">Contact Us</h2>
                    <p class="section-subtitle">
                        If you have any questions about our products or services, please feel free to contact us
                    </p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">📧</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">邮箱联系</h3>
                            <p class="feature-description">
                                <strong><EMAIL></strong><br>
                                工作日 24 小时内回复
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Email Contact</h3>
                            <p class="feature-description">
                                <strong><EMAIL></strong><br>
                                Reply within 24 hours on weekdays
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">💬</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">在线客服</h3>
                            <p class="feature-description">
                                通过控制台获得实时支持<br>
                                <a href="/console/login" class="btn" style="margin-top: 1rem;">进入控制台</a>
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Online Support</h3>
                            <p class="feature-description">
                                Get real-time support through console<br>
                                <a href="/console/login" class="btn" style="margin-top: 1rem;">Enter Console</a>
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🌐</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">社交媒体</h3>
                            <p class="feature-description">
                                关注我们获取最新动态<br>
                                GitHub | Twitter | LinkedIn
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Social Media</h3>
                            <p class="feature-description">
                                Follow us for latest updates<br>
                                GitHub | Twitter | LinkedIn
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>


    </main>

    <!-- 页脚容器 -->
    <div id="footer-container"></div>

    <!-- JavaScript -->
    <script src="/js/main.js"></script>
</body>
</html>
