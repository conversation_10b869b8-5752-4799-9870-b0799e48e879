// Simple Node.js script to generate animus 28x28 PNG
// Run with: node generate-animus.js

const fs = require('fs');

// Create a simple PNG manually using basic PNG structure
function createSimplePNG() {
    // This creates a very basic 28x28 PNG with blue background and white "A"
    // For a proper solution, you would use canvas or sharp library
    
    console.log('🎨 Generating animus 28x28 PNG...');
    console.log('');
    console.log('📋 Specifications:');
    console.log('   • Size: 28×28 pixels');
    console.log('   • Format: PNG');
    console.log('   • Content: "animus" branding');
    console.log('   • Max size: 300KB');
    console.log('');
    console.log('⚠️  Note: This script requires additional libraries.');
    console.log('');
    console.log('🔧 To generate the PNG, please:');
    console.log('   1. Open animus-generator.html in your browser');
    console.log('   2. Click "Generate Image"');
    console.log('   3. Click "Download animus-28x28.png"');
    console.log('');
    console.log('🌐 Alternative: Use the HTML generator at:');
    console.log('   file://' + __dirname + '/animus-generator.html');
    console.log('');
    console.log('📁 The file will be saved to your Downloads folder.');
}

// Alternative: Create SVG that can be converted to PNG
function createSVGTemplate() {
    const svgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="28" height="28" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea"/>
      <stop offset="50%" style="stop-color:#764ba2"/>
      <stop offset="100%" style="stop-color:#667eea"/>
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="28" height="28" fill="url(#bg-gradient)"/>
  
  <!-- Design elements -->
  <circle cx="8" cy="8" r="4" fill="rgba(255,255,255,0.2)"/>
  <circle cx="20" cy="20" r="5" fill="rgba(255,255,255,0.1)"/>
  
  <!-- Animus "A" text -->
  <text x="14" y="18" font-family="Arial, sans-serif" font-size="12" font-weight="bold" 
        text-anchor="middle" fill="white">A</text>
</svg>`;

    fs.writeFileSync('animus-28x28.svg', svgContent);
    console.log('✅ Created animus-28x28.svg template');
    console.log('💡 Convert to PNG using online tools or:');
    console.log('   • Inkscape: inkscape animus-28x28.svg --export-png=animus-28x28.png --export-width=28 --export-height=28');
    console.log('   • ImageMagick: magick animus-28x28.svg animus-28x28.png');
}

if (require.main === module) {
    createSimplePNG();
    createSVGTemplate();
}

module.exports = { createSimplePNG, createSVGTemplate }; 