#!/usr/bin/env python3
"""
Simple image generator for creating placeholder PNG images
Generates 28x28 and 108x108 PNG images under 300KB
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
except ImportError:
    print("PIL (Pillow) is required. Install with: pip install Pillow")
    exit(1)

def create_simple_image(width, height, filename, text="AI"):
    """Create a simple PNG image with specified dimensions"""
    
    # Create a new image with RGBA mode (supports transparency)
    image = Image.new('RGBA', (width, height), (255, 255, 255, 0))
    draw = ImageDraw.Draw(image)
    
    # Create a gradient background
    for y in range(height):
        # Simple gradient from blue to light blue
        blue_intensity = int(100 + (155 * y / height))
        color = (50, 100, blue_intensity, 255)
        draw.line([(0, y), (width, y)], fill=color)
    
    # Add text if the image is large enough
    if width >= 20 and height >= 20:
        # Try to use a default font, fallback to built-in if not available
        try:
            # Calculate font size based on image dimensions
            font_size = min(width, height) // 4
            font = ImageFont.truetype("arial.ttf", font_size)
        except (OSError, IOError):
            # Fallback to default font
            font = ImageFont.load_default()
        
        # Get text size using textbbox
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # Center the text
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        # Add white text with black outline for better visibility
        for adj in range(-1, 2):
            for adj2 in range(-1, 2):
                draw.text((x + adj, y + adj2), text, font=font, fill=(0, 0, 0, 255))
        draw.text((x, y), text, font=font, fill=(255, 255, 255, 255))
    
    # Save the image
    image.save(filename, 'PNG', optimize=True)
    
    # Check file size
    file_size = os.path.getsize(filename)
    print(f"Created {filename}: {width}x{height} pixels, {file_size} bytes")
    
    if file_size > 300 * 1024:  # 300KB
        print(f"WARNING: {filename} exceeds 300KB limit ({file_size} bytes)")
    
    return file_size

def main():
    """Generate the required images"""
    
    # Change to the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("Generating PNG images...")
    
    # Generate 28x28 image
    size1 = create_simple_image(28, 28, "icon-28x28.png", "Animus")
    
    # Generate 108x108 image  
    size2 = create_simple_image(108, 108, "icon-108x108.png", "Animus")
    
    print(f"\nImages generated successfully!")
    print(f"icon-28x28.png: {size1} bytes")
    print(f"icon-108x108.png: {size2} bytes")
    print("Both images are under the 300KB limit.")

if __name__ == "__main__":
    main() 