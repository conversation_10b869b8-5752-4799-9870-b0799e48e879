<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="3" stdDeviation="4" flood-color="#2563eb" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Logo Icon Background -->
  <rect x="5" y="10" width="40" height="40" rx="10" ry="10" fill="url(#logoGradient)" filter="url(#shadow)"/>
  
  <!-- Logo Icon Letter "A" -->
  <text x="25" y="38" font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif" 
        font-size="24" font-weight="700" text-anchor="middle" fill="white">AI</text>
  
  <!-- Logo Text "Animus" -->
  <text x="55" y="38" font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif" 
        font-size="28" font-weight="800" fill="url(#textGradient)" letter-spacing="-0.5px">Animus</text>
</svg>
