<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animus AI - Image Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
        }
        .generator-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        .canvas-container {
            margin: 20px 0;
            text-align: center;
        }
        canvas {
            border: 1px solid #cbd5e1;
            border-radius: 4px;
            margin: 10px;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            margin: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-1px);
        }
        button:active {
            transform: translateY(0);
        }
        .info {
            background: #e0f2fe;
            border: 1px solid #0891b2;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #0c4a6e;
        }
        .success {
            background: #dcfce7;
            border: 1px solid #16a34a;
            color: #15803d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Animus AI Image Generator</h1>
        
        <div class="info">
            <strong>📋 Requirements:</strong><br>
            • 28×28 pixels PNG (≤ 300KB)<br>
            • 108×108 pixels PNG (≤ 300KB)<br>
            • For icons and app assets
        </div>

        <div class="generator-section">
            <h3>🔷 Small Icon (28×28 pixels)</h3>
            <div class="canvas-container">
                <canvas id="canvas28" width="28" height="28"></canvas>
                <canvas id="preview28" width="140" height="140" style="image-rendering: pixelated;"></canvas>
            </div>
            <button onclick="generateImage(28)">Generate 28×28 Image</button>
            <button onclick="downloadImage('canvas28', 'animus-icon-28x28.png')">📥 Download PNG</button>
        </div>

        <div class="generator-section">
            <h3>🔷 Large Icon (108×108 pixels)</h3>
            <div class="canvas-container">
                <canvas id="canvas108" width="108" height="108"></canvas>
                <canvas id="preview108" width="216" height="216"></canvas>
            </div>
            <button onclick="generateImage(108)">Generate 108×108 Image</button>
            <button onclick="downloadImage('canvas108', 'animus-icon-108x108.png')">📥 Download PNG</button>
        </div>

        <div class="generator-section">
            <h3>🎯 Quick Generate Both</h3>
            <button onclick="generateBoth()">Generate Both Images</button>
            <button onclick="downloadBoth()">📦 Download Both PNGs</button>
        </div>

        <div id="status" class="info" style="display: none;"></div>
    </div>

    <script>
        function createGradient(ctx, width, height) {
            // Create a beautiful gradient background
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(0.5, '#764ba2');
            gradient.addColorStop(1, '#667eea');
            return gradient;
        }

        function generateImage(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            const preview = document.getElementById(`preview${size}`);
            const previewCtx = preview.getContext('2d');

            // Clear canvas
            ctx.clearRect(0, 0, size, size);

            // Set background gradient
            ctx.fillStyle = createGradient(ctx, size, size);
            ctx.fillRect(0, 0, size, size);

            // Add some geometric shapes for a modern look
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.beginPath();
            ctx.arc(size * 0.3, size * 0.3, size * 0.15, 0, Math.PI * 2);
            ctx.fill();

            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.beginPath();
            ctx.arc(size * 0.7, size * 0.7, size * 0.2, 0, Math.PI * 2);
            ctx.fill();

            // Add "AI" text for larger sizes
            if (size >= 28) {
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                if (size >= 100) {
                    ctx.font = `bold ${size * 0.25}px Arial`;
                    ctx.fillText('Animus', size / 2, size / 2);
                } else {
                    // For small 28x28 canvas, use smaller font and shorter text
                    ctx.font = `bold ${size * 0.25}px Arial`;
                    ctx.fillText('A', size / 2, size / 2);
                }
            }

            // Update preview (scaled up version)
            previewCtx.clearRect(0, 0, preview.width, preview.height);
            previewCtx.imageSmoothingEnabled = false;
            previewCtx.drawImage(canvas, 0, 0, preview.width, preview.height);

            showStatus(`✅ Generated ${size}×${size} image successfully!`, 'success');
        }

        function downloadImage(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            
            // Convert to blob and check size
            canvas.toBlob(function(blob) {
                const sizeKB = (blob.size / 1024).toFixed(2);
                
                if (blob.size > 300 * 1024) {
                    showStatus(`⚠️ Warning: ${filename} is ${sizeKB}KB (exceeds 300KB limit)`, 'error');
                } else {
                    showStatus(`✅ ${filename} ready for download (${sizeKB}KB)`, 'success');
                }

                // Create download link
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/png');
        }

        function generateBoth() {
            generateImage(28);
            generateImage(108);
            showStatus('🎉 Both images generated successfully!', 'success');
        }

        function downloadBoth() {
            downloadImage('canvas28', 'animus-icon-28x28.png');
            setTimeout(() => {
                downloadImage('canvas108', 'animus-icon-108x108.png');
            }, 500);
        }

        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = type === 'success' ? 'success' : 'info';
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }

        // Auto-generate images when page loads
        window.addEventListener('load', function() {
            setTimeout(() => {
                generateBoth();
                showStatus('🚀 Welcome! Images auto-generated and ready for download.', 'success');
            }, 500);
        });
    </script>
</body>
</html> 