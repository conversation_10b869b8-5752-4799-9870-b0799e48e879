<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- 中文标题 -->
    <title data-lang="zh" class="lang-content active">核心特性 - animus.run</title>
    <!-- 英文标题 -->
    <title data-lang="en" class="lang-content">Core Features - animus.run</title>

    <meta name="description" content="探索 animus.run AI Agent Sandbox 平台的强大功能和技术特性">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="icon" href="image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚡</text></svg>">
</head>
<body>
    <!-- 页眉容器 -->
    <div id="header-container"></div>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero" style="padding: 4rem 0;">
            <div class="container">
                <div class="hero-content fade-in-up">
                    <!-- 中文 Hero -->
                    <div class="lang-content active" data-lang="zh">
                        <h1>强大的平台特性</h1>
                        <p>深入了解 animus.run 为 AI Agent 开发提供的完整功能生态</p>
                    </div>

                    <!-- 英文 Hero -->
                    <div class="lang-content" data-lang="en">
                        <h1>Powerful Platform Features</h1>
                        <p>Explore the complete functional ecosystem that animus.run provides for AI Agent development</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 核心架构特性 -->
        <section class="section">
            <div class="container">
                <!-- 中文标题 -->
                <h2 class="section-title lang-content active" data-lang="zh">核心架构特性</h2>
                <!-- 英文标题 -->
                <h2 class="section-title lang-content" data-lang="en">Core Architecture Features</h2>

                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🏗️</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">云原生架构</h3>
                            <p class="feature-description">
                                基于 Kubernetes 构建的微服务架构，支持弹性扩展、自动故障恢复和负载均衡。
                                提供 99.9% 的可用性保证，确保您的 AI Agent 持续稳定运行。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Cloud-Native Architecture</h3>
                            <p class="feature-description">
                                Microservices architecture built on Kubernetes, supporting elastic scaling, automatic fault recovery,
                                and load balancing. Provides 99.9% availability guarantee to ensure your AI Agents run continuously and stably.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🔒</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">多层安全防护</h3>
                            <p class="feature-description">
                                采用多租户隔离、网络隔离、数据加密和访问控制等多层安全机制。
                                通过 SOC 2 Type II 认证，符合企业级安全标准。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Multi-Layer Security Protection</h3>
                            <p class="feature-description">
                                Employs multi-tenant isolation, network isolation, data encryption, and access control for multi-layered security.
                                SOC 2 Type II certified, meeting enterprise-grade security standards.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">⚡</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">高性能计算</h3>
                            <p class="feature-description">
                                提供 NVIDIA A100、V100 等高端 GPU 资源，支持 CUDA、cuDNN 加速计算。
                                自动优化资源分配，实现成本效益最大化。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">High-Performance Computing</h3>
                            <p class="feature-description">
                                Provides high-end GPU resources like NVIDIA A100 and V100, supporting CUDA and cuDNN accelerated computing.
                                Automatically optimizes resource allocation for maximum cost-effectiveness.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 开发环境特性 -->
        <section class="section" style="background: var(--bg-secondary);">
            <div class="container">
                <!-- 中文标题 -->
                <h2 class="section-title lang-content active" data-lang="zh">开发环境特性</h2>
                <!-- 英文标题 -->
                <h2 class="section-title lang-content" data-lang="en">Development Environment Features</h2>

                <div class="content-card">
                    <div class="features-grid">
                        <div class="feature-card">
                            <span class="feature-icon">🧠</span>
                            <!-- 中文 -->
                            <div class="lang-content active" data-lang="zh">
                                <h3 class="feature-title">AI 框架支持</h3>
                                <p class="feature-description">
                                    内置支持 TensorFlow、PyTorch、JAX、Hugging Face Transformers、
                                    LangChain 等主流 AI 框架，预配置常用的机器学习库和工具。
                                </p>
                            </div>
                            <!-- 英文 -->
                            <div class="lang-content" data-lang="en">
                                <h3 class="feature-title">AI Framework Support</h3>
                                <p class="feature-description">
                                    Built-in support for mainstream AI frameworks including TensorFlow, PyTorch, JAX,
                                    Hugging Face Transformers, LangChain, with pre-configured machine learning libraries and tools.
                                </p>
                            </div>
                        </div>
                        <div class="feature-card">
                            <span class="feature-icon">💻</span>
                            <!-- 中文 -->
                            <div class="lang-content active" data-lang="zh">
                                <h3 class="feature-title">集成开发环境</h3>
                                <p class="feature-description">
                                    提供基于 Web 的 IDE，支持 Jupyter Notebook、VS Code Server、
                                    以及自定义开发环境配置，支持多种编程语言。
                                </p>
                            </div>
                            <!-- 英文 -->
                            <div class="lang-content" data-lang="en">
                                <h3 class="feature-title">Integrated Development Environment</h3>
                                <p class="feature-description">
                                    Provides web-based IDE supporting Jupyter Notebook, VS Code Server,
                                    and custom development environment configurations with multi-language support.
                                </p>
                            </div>
                        </div>
                        <div class="feature-card">
                            <span class="feature-icon">📦</span>
                            <!-- 中文 -->
                            <div class="lang-content active" data-lang="zh">
                                <h3 class="feature-title">容器化部署</h3>
                                <p class="feature-description">
                                    基于 Docker 容器技术，支持自定义镜像、环境版本管理和一键部署。
                                    与 Docker Hub、Harbor 等镜像仓库无缝集成。
                                </p>
                            </div>
                            <!-- 英文 -->
                            <div class="lang-content" data-lang="en">
                                <h3 class="feature-title">Containerized Deployment</h3>
                                <p class="feature-description">
                                    Based on Docker container technology, supporting custom images, environment version management,
                                    and one-click deployment. Seamlessly integrates with Docker Hub, Harbor and other image repositories.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- AI Agent 管理特性 -->
        <section class="section">
            <div class="container">
                <!-- 中文标题 -->
                <h2 class="section-title lang-content active" data-lang="zh">AI Agent 管理特性</h2>
                <!-- 英文标题 -->
                <h2 class="section-title lang-content" data-lang="en">AI Agent Management Features</h2>

                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🤖</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">Agent 生命周期管理</h3>
                            <p class="feature-description">
                                完整的 AI Agent 生命周期管理，从创建、训练、测试到部署、监控和更新。
                                支持 A/B 测试、灰度发布和回滚机制。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Agent Lifecycle Management</h3>
                            <p class="feature-description">
                                Complete AI Agent lifecycle management from creation, training, testing to deployment, monitoring, and updates.
                                Supports A/B testing, canary releases, and rollback mechanisms.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🔧</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">配置管理</h3>
                            <p class="feature-description">
                                灵活的配置管理系统，支持环境变量、配置文件和密钥管理。
                                提供配置模板和最佳实践建议。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Configuration Management</h3>
                            <p class="feature-description">
                                Flexible configuration management system supporting environment variables, configuration files, and secret management.
                                Provides configuration templates and best practice recommendations.
                            </p>
                        </div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📊</span>
                        <!-- 中文 -->
                        <div class="lang-content active" data-lang="zh">
                            <h3 class="feature-title">实时监控</h3>
                            <p class="feature-description">
                                实时监控 AI Agent 的运行状态、性能指标和资源使用情况。
                                提供详细的日志记录、错误追踪和性能分析。
                            </p>
                        </div>
                        <!-- 英文 -->
                        <div class="lang-content" data-lang="en">
                            <h3 class="feature-title">Real-time Monitoring</h3>
                            <p class="feature-description">
                                Real-time monitoring of AI Agent runtime status, performance metrics, and resource usage.
                                Provides detailed logging, error tracking, and performance analysis.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 未来规划 -->
        <section class="section" style="background: var(--bg-gradient); color: white;">
            <div class="container">
                <div style="text-align: center;">
                    <!-- 中文 -->
                    <div class="lang-content active" data-lang="zh">
                        <h2 class="section-title" style="color: white;">未来功能规划</h2>
                        <p class="section-subtitle" style="color: rgba(255, 255, 255, 0.9);">
                            我们持续创新，不断扩展平台能力，为您提供更强大的 AI Agent 开发体验
                        </p>
                    </div>
                    <!-- 英文 -->
                    <div class="lang-content" data-lang="en">
                        <h2 class="section-title" style="color: white;">Future Feature Roadmap</h2>
                        <p class="section-subtitle" style="color: rgba(255, 255, 255, 0.9);">
                            We continuously innovate and expand platform capabilities to provide you with a more powerful AI Agent development experience
                        </p>
                    </div>

                    <div class="features-grid" style="margin-top: 3rem;">
                        <div class="feature-card" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2);">
                            <span class="feature-icon">🧬</span>
                            <!-- 中文 -->
                            <div class="lang-content active" data-lang="zh">
                                <h3 class="feature-title" style="color: white;">Auto ML</h3>
                                <p class="feature-description" style="color: rgba(255, 255, 255, 0.8);">
                                    自动化机器学习工具，自动选择算法、调优超参数，
                                    让非专业人员也能轻松构建高质量的 AI 模型。
                                </p>
                            </div>
                            <!-- 英文 -->
                            <div class="lang-content" data-lang="en">
                                <h3 class="feature-title" style="color: white;">Auto ML</h3>
                                <p class="feature-description" style="color: rgba(255, 255, 255, 0.8);">
                                    Automated machine learning tools that automatically select algorithms and tune hyperparameters,
                                    enabling non-experts to easily build high-quality AI models.
                                </p>
                            </div>
                        </div>
                        <div class="feature-card" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2);">
                            <span class="feature-icon">🎯</span>
                            <!-- 中文 -->
                            <div class="lang-content active" data-lang="zh">
                                <h3 class="feature-title" style="color: white;">智能推荐</h3>
                                <p class="feature-description" style="color: rgba(255, 255, 255, 0.8);">
                                    基于历史数据和最佳实践的智能推荐系统，
                                    为您的项目推荐合适的工具、框架和配置。
                                </p>
                            </div>
                            <!-- 英文 -->
                            <div class="lang-content" data-lang="en">
                                <h3 class="feature-title" style="color: white;">Intelligent Recommendations</h3>
                                <p class="feature-description" style="color: rgba(255, 255, 255, 0.8);">
                                    Intelligent recommendation system based on historical data and best practices,
                                    recommending suitable tools, frameworks, and configurations for your projects.
                                </p>
                            </div>
                        </div>
                        <div class="feature-card" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2);">
                            <span class="feature-icon">🌍</span>
                            <!-- 中文 -->
                            <div class="lang-content active" data-lang="zh">
                                <h3 class="feature-title" style="color: white;">联邦学习</h3>
                                <p class="feature-description" style="color: rgba(255, 255, 255, 0.8);">
                                    支持联邦学习框架，在保护数据隐私的前提下，
                                    实现多方协作训练和模型共享。
                                </p>
                            </div>
                            <!-- 英文 -->
                            <div class="lang-content" data-lang="en">
                                <h3 class="feature-title" style="color: white;">Federated Learning</h3>
                                <p class="feature-description" style="color: rgba(255, 255, 255, 0.8);">
                                    Supports federated learning frameworks, enabling multi-party collaborative training
                                    and model sharing while protecting data privacy.
                                </p>
                            </div>
                        </div>
                    </div>

                    <a href="/contact.html" class="cta-button" style="margin-top: 2rem;">
                        <span class="lang-inline active" data-lang="zh">了解更多详情</span>
                        <span class="lang-inline" data-lang="en">Learn More</span>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚容器 -->
    <div id="footer-container"></div>

    <!-- JavaScript -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/templates.js"></script>
</body>
</html>
