# AI Dashboard

A full-stack AI dashboard built with React (Ant Design), Golang, Logto, and Supabase.

## Project Structure

```
.
├── frontend/           # React frontend application
│   ├── src/           # Source code
│   ├── public/        # Static files
│   └── package.json   # Frontend dependencies
│
├── backend/           # Golang backend application
│   ├── cmd/          # Application entry points
│   ├── internal/     # Private application code
│   ├── pkg/          # Public library code
│   └── go.mod        # Go module file
│
└── README.md         # This file
```

## Prerequisites

- Node.js 18+ and npm
- Go 1.21+
- Supabase account
- Logto account

## Environment Variables

### Frontend (.env)
```
VITE_API_URL=http://localhost:8080
```

### Backend (.env or config.yaml)
```
PORT=8080
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
LOGTO_ENDPOINT=your_logto_endpoint
LOGTO_APP_ID=your_logto_app_id
LOGTO_APP_SECRET=your_logto_app_secret
```

Note: Authentication is handled entirely by the backend. The frontend only needs to know the API URL. Backend configuration can be set via environment variables or the `backend/config.yaml` file.

## Getting Started

1. Clone the repository
2. Set up environment variables
3. Install dependencies:
   ```bash
   # Frontend
   cd frontend
   npm install

   # Backend
   cd backend
   go mod download
   go install github.com/air-verse/air@latest  # 安装热重载工具
   ```

4. 开发环境启动（使用热重载）:
   ```bash
   # 使用一键启动脚本同时启动前端和后端
   ./start-dev.ps1

   # 或者单独启动:
   
   # 前端开发服务器 (支持热重载)
   cd frontend
   npm run dev

   # 后端开发服务器 (支持热重载)
   cd backend
   ./dev.ps1
   
   # 或者不使用热重载启动后端
   cd backend
   go run cmd/server/main.go
   ```

5. 生产环境构建:
   ```bash
   # 构建前端
   cd frontend
   npm run build

   # 构建后端
   cd backend
   go build -o server.exe ./cmd/server
   ```

## Features

- 🤖 **AI Chat Interface** - 支持多种 LLM 提供商的智能对话
- 🔑 **API Key Management** - 安全的 API 密钥管理系统
- 💳 **Credit/Payment System** - 完整的积分和支付系统
- 🔐 **User Authentication** - 基于 Logto 的用户认证
- 📊 **Data Storage** - 使用 Supabase 的数据存储
- 🌐 **Static Website** - 集成的官方网站
- 🔧 **System Providers** - 内置系统级 LLM 提供商

## 📚 文档

详细的项目文档已整理到 `docs/` 目录下：

- **[📖 完整文档索引](docs/README.md)** - 所有文档的入口
- **[🔧 开发文档](docs/development/)** - 开发指南和实现细节
- **[🚀 部署文档](docs/deployment/)** - 部署和运维指南
- **[🔍 故障排除](docs/troubleshooting/)** - 问题诊断和解决方案
- **[📡 API 文档](docs/api/)** - API 测试和集成指南

### 快速链接

- **新手入门**：[环境配置](docs/development/ENVIRONMENT_CONFIG.md)
- **功能实现**：[聊天功能实现](docs/development/CHAT_IMPLEMENTATION.md)
- **部署指南**：[Docker 部署](docs/deployment/DOCKER_DEPLOYMENT.md)
- **问题排查**：[登录问题排查](docs/troubleshooting/LOGIN_TROUBLESHOOTING.md)

## 🚀 快速开始

### 开发环境

```bash
# 一键启动开发环境
./start-dev.ps1

# 或者分别启动
cd frontend && npm run dev    # 前端开发服务器
cd backend && ./dev.ps1      # 后端开发服务器（热重载）
```

### 生产环境

```bash
# Docker 部署
docker-compose up -d

# 或手动构建
./build.sh
```

### 测试

```bash
# 运行聊天功能测试
node docs/api/test-chat-integration.js

# 运行生产环境诊断
node docs/troubleshooting/debug-login-production.js
```

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 License

MIT License - 详见 [LICENSE](LICENSE) 文件