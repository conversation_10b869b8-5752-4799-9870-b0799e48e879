# Multi-stage Dockerfile for Animus Web Application
# This builds both the frontend React app and the Go backend server

# Stage 1: Build Frontend
FROM node:24-alpine3.21 AS frontend-builder

# Set working directory for frontend
WORKDIR /app/frontend

# Copy package files
COPY frontend/package*.json ./

# Install dependencies
#RUN npm ci --only=production
RUN npm install

# Copy frontend source code
COPY frontend/ ./

# Build the frontend application
RUN npm run build

# Stage 2: Build Backend
FROM golang:1.23-alpine AS backend-builder

# Install git (needed for go modules)
RUN apk add --no-cache git

# Set working directory for backend
WORKDIR /app/backend

# Copy go mod files
COPY backend/go.mod backend/go.sum ./

# Download dependencies
RUN go mod download

# Copy backend source code
COPY backend/ ./

# Build the Go application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o animus-server ./cmd/server

# Stage 3: Final Runtime Image
FROM alpine:latest

ENV TZ=Asia/Shanghai

# Install ca-certificates for HTTPS requests and sqlite for database
RUN apk --no-cache add ca-certificates sqlite tzdata

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create app directory
WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S animus && \
    adduser -S animus -u 1001

# Copy the built backend binary
COPY --from=backend-builder /app/backend/animus-server .

# Copy the built frontend files
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist

# Copy the static website files
COPY website/static ./website/static

# Copy configuration files
COPY backend/config*.yaml ./
COPY backend/schema.sql ./

# Create data directory for SQLite database
RUN mkdir -p /app/data && chown -R animus:animus /app

# Switch to non-root user
USER animus

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/ || exit 1

# Set environment variables
ENV APP_ENV=production
ENV GIN_MODE=release
ENV PORT=8080

# Run the application
CMD ["./animus-server"]
