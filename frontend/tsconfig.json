{"compilerOptions": {"jsx": "react-jsx", "target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "allowSyntheticDefaultImports": true, "lib": ["ESNext", "DOM", "DOM.Iterable"], "types": ["vite/client"]}, "include": ["src"], "exclude": ["node_modules", "dist"]}