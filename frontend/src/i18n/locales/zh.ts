const translation = {
  // 通用
  common: {
    loading: '加载中...',
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    create: '创建',
    search: '搜索',
    submit: '提交',
    back: '返回',
    success: '成功',
    deleteSuccess: '删除成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    menu: '菜单',
    copied: '已复制到剪贴板',
    titleUpdated: '标题已更新',
    copy: '复制',
    refresh: '刷新',
    close: '关闭',
    next: '下一步',
    previous: '上一步',
    reset: '重置',
    clear: '清空',
    enabled: '启用',
    disabled: '禁用',
    download: '下载',
    upload: '上传',
    import: '导入',
    export: '导出',
    select: '选择',
    selectAll: '全选',
    unselectAll: '取消全选',
    expand: '展开',
    collapse: '收起',
    total: '总计',
    noData: '暂无数据',
    operation: '操作',
    name: '名称',
    description: '描述',
    status: '状态',
    type: '类型',
    createTime: '创建时间',
    updateTime: '更新时间',
    actions: '操作',
    pagination: '第 {start}-{end} 条，共 {total} 条',
  },

  // 导航栏
  navbar: {
    title: 'Animus',
    chat: '对话',
    apiKeys: 'API 密钥',
    llmConfig: 'LLM 配置',
    mcpServers: 'MCP 服务',
    agentPods: 'Agent Pod',
    credits: '积分',
    documentation: '文档',
    logout: '退出登录',
    showNavigation: '显示导航栏',
  },

  // 错误信息
  errors: {
    generic: '发生错误',
    network: '网络错误，请检查网络连接',
    request: '创建请求时出错，请重试。',
    unknown: '未知错误',
    unauthorized: '未授权，请重新登录',
    forbidden: '权限不足',
    notFound: '资源未找到',
    conversationNotFound: '对话未找到',
    badRequest: '请求参数错误',
    paymentRequired: '需要付款才能继续。',
    insufficientCredits: '积分不足',
    conflict: '数据冲突',
    tooManyRequests: '请求过多，请稍后再试。',
    serverError: '服务器错误，请稍后重试',
    titleUpdateFailed: '更新标题失败，请重试。',
    validationFailed: '数据验证失败',
    timeout: '请求超时',
  },

  // 登录页面
  login: {
    title: '登录',
    welcomeBack: '欢迎回来',
    pleaseSignIn: '请登录您的账户',
    loginWithGitHub: '使用 GitHub 登录',
    loginWithGoogle: '使用 Google 登录',
    loginButton: '登录',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    processing: '正在处理登录请求...',
    redirecting: '登录流程异常，正在返回首页...',
  },

  // API密钥页面
  apiKeys: {
    title: 'API 密钥',
    createNewKey: '创建新密钥',
    createNew: '创建新密钥',
    keyName: '密钥名称',
    name: '名称',
    key: '密钥',
    keyNamePlaceholder: '输入密钥名称',
    enterName: '输入密钥名称',
    keyCreated: 'API 密钥已创建',
    keyDeleted: 'API 密钥已删除',
    keyCreateError: '创建 API 密钥失败',
    createError: '创建 API 密钥失败',
    createSuccess: 'API 密钥创建成功',
    keyDeleteError: '删除 API 密钥失败',
    deleteError: '删除 API 密钥失败',
    deleteSuccess: 'API 密钥删除成功',
    fetchError: '获取数据失败',
    confirmDelete: '确定要删除这个 API 密钥吗？',
    deleteConfirm: '确定要删除这个 API 密钥吗？',
    copyKey: '复制密钥',
    copySuccess: '复制成功',
    keyCopied: '密钥已复制',
    lastUsed: '最后使用',
    created: '创建时间',
    totalRequests: '总请求数',
    requests: '请求数',
    tokens: '令牌数',
    actions: '操作',
    createdAt: '创建时间',
    never: '从未',
    keyValue: '密钥值',
    keyStats: '密钥统计',
    statsTitle: '使用统计',
    loadingStats: '加载统计中...',
    totalTokens: '总令牌数',
    usageDistribution: '使用分布',
    tokensDistribution: '令牌分布',
    dailyUsage: '每日使用量',
    dailyTokensUsage: '每日令牌使用量',
    noStats: '暂无统计数据',
    totalApiKeys: 'API 密钥总数',
    averageRequestsPerKey: '平均请求数',
    keyNameRequired: '请输入密钥名称',
    nameRequired: '请输入密钥名称',
  },

  // 积分页面
  credits: {
    title: '积分管理',
    balance: '余额',
    currentBalance: '当前余额',
    topUp: '充值',
    addCredits: '充值积分',
    history: '交易历史',
    amount: '金额',
    description: '描述',
    date: '日期',
    status: '状态',
    selectAmount: '选择金额',
    customAmount: '自定义金额',
    enterAmount: '输入金额',
    amountRequired: '请输入金额',
    amountPositive: '金额必须大于0',
    paymentMethod: '支付方式',
    paymentProvider: '支付提供商',
    confirmTopUp: '确认充值',
    topUpSuccess: '充值成功',
    topUpError: '充值失败',
    transaction: '交易',
    transactionId: '交易ID',
    transactionType: '交易类型',
    transactionAmount: '交易金额',
    transactionDate: '交易日期',
    transactionStatus: '交易状态',
    noTransactions: '暂无交易记录',
    pending: '处理中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    topup: '充值',
    consume: '消费',
    refund: '退款',
    bonus: '奖励',
    insufficient: '余额不足',
    minimum: '最小',
    maximum: '最大',
    provider: '提供商',
    selectProvider: '选择支付提供商',
    providerRequired: '请选择支付提供商',
    processing: '处理中...',
    redirecting: '跳转中...',
    paymentUrl: '支付链接',
    openPayment: '打开支付页面',
    checkStatus: '检查状态',
    paymentCompleted: '支付完成',
    paymentFailed: '支付失败',
    paymentCancelled: '支付取消',
    invalidAmount: '请输入有效金额',
    minAmount: '最小充值金额为 {amount} 积分',
    maxAmount: '最大充值金额为 {amount} 积分',
  },

  // 聊天页面
  chat: {
    title: '对话',
    newChat: '新对话',
    startNewConversation: '开始新对话',
    send: '发送',
    typeMessage: '输入消息...',
    askSomething: '问一问 Animus...',
    conversationTitle: '对话标题',
    emptyState: '开始一次新的对话',
    conversationCleared: '对话已清空',
    conversationSaved: '对话已保存',
    conversationDownloaded: '对话已下载',
    cannotSaveEmpty: '无法保存空对话',
    messageEmpty: '消息不能为空',
    responseLoading: '正在生成回复...',
    currentModel: '当前模型',
    selectProvider: '选择模型',
    noFileSelected: '未选择文件',
    fileSent: '文件已发送',
    imageReceived: '收到图片',
    fileReceived: '收到文件',
    uploadFile: '上传文件',
    clickOrDragFile: '点击或拖拽文件到此处上传',
    supportedFileTypes: '支持图片和文档文件',
    addMessageWithFile: '为文件添加消息（可选）',
    aiResponding: 'AI 正在回复中...',
    interrupt: '中断',
    statusReady: '准备中',
    statusThinking: '思考中',
    statusRunning: '运行中',
    statusFailed: '失败',
  },

  // LLM API 配置页面
  llmConfig: {
    title: 'LLM 提供商配置',
    addProvider: '添加提供商',
    editProvider: '编辑提供商',
    providerName: '提供商名称',
    providerAlias: '别名',
    providerType: '提供商类型',
    apiType: 'API 类型',
    apiKey: 'API 密钥',
    baseURL: '基础 URL',
    model: '模型',
    saved: '保存成功',
    deleted: '删除成功',
    deleteProvider: '删除提供商',
    deleteConfirm: '确定要删除这个提供商吗？',
    testSuccess: '连接测试成功',
    defaultProvider: '默认提供商',
    setAsDefault: '设为默认',
    noProviders: '暂无提供商',
    addFirst: '添加第一个提供商',
    required: '此字段为必填项',
    advanced: '高级选项',
    temperature: '温度',
    maxTokens: '最大令牌数',
    topP: 'Top P',
    frequencyPenalty: '频率惩罚',
    presencePenalty: '存在惩罚',
    save: '保存',
    cancel: '取消',
    testConnection: '测试连接',
    defaultLabel: '默认',
    manageProviders: '管理提供商',
  },

  // MCP 服务页面
  mcpServers: {
    title: 'MCP 服务',
    addServer: '添加服务',
    editServer: '编辑服务',
    name: '名称',
    namePlaceholder: '输入服务名称',
    nameRequired: '请输入服务名称',
    description: '描述',
    descriptionPlaceholder: '输入服务描述（可选）',
    type: '连接类型',
    typePlaceholder: '选择连接类型',
    typeRequired: '请选择连接类型',
    status: '状态',
    updatedAt: '更新时间',
    formMode: '表单模式',
    jsonMode: 'JSON 模式',
    
    // StdIO Configuration
    command: '命令',
    commandPlaceholder: '输入执行命令',
    commandRequired: '请输入执行命令',
    args: '参数',
    argsPlaceholder: '输入命令参数',
    envVars: '环境变量',
    envVarsPlaceholder: 'KEY1=value1\nKEY2=value2\n...',
    
    // SSE Configuration
    url: 'URL',
    urlPlaceholder: '输入 SSE 服务 URL',
    urlRequired: '请输入 URL',
    urlInvalid: '请输入有效的 URL',
    sseUrlPlaceholder: 'http://localhost:3000/sse',
    headers: '请求头',
    headersPlaceholder: 'Authorization: Bearer token\nContent-Type: application/json\n...',
    
    // HTTP Configuration
    baseUrl: '基础 URL',
    baseUrlPlaceholder: '输入 HTTP 服务基础 URL',
    baseUrlRequired: '请输入基础 URL',
    baseUrlInvalid: '请输入有效的基础 URL',
    httpBaseUrlPlaceholder: 'http://localhost:3000',
    timeout: '超时时间',
    timeoutPlaceholder: '30',
    retryCount: '重试次数',
    retryCountPlaceholder: '3',
    seconds: '秒',
    
    // JSON Editor
    jsonEditorHelp: '在这里您可以直接编辑 JSON 配置。请确保 JSON 格式正确。',
    jsonParseError: 'JSON 格式错误，请检查语法',
    jsonValid: 'JSON 格式正确',
    validateJson: '验证 JSON',
    
    // Messages
    createSuccess: 'MCP 服务创建成功',
    updateSuccess: 'MCP 服务更新成功',
    deleteSuccess: 'MCP 服务删除成功',
    toggleSuccess: 'MCP 服务状态更新成功',
    createError: '创建 MCP 服务失败',
    updateError: '更新 MCP 服务失败',
    deleteError: '删除 MCP 服务失败',
    toggleError: '更新 MCP 服务状态失败',
    deleteConfirm: '确定要删除这个 MCP 服务吗？',
    loadTypesError: '加载服务类型失败',
    
    // Types
    stdioType: 'Standard I/O',
    stdioDescription: '通过标准输入/输出连接到 MCP 服务',
    sseType: 'Server-Sent Events',
    sseDescription: '通过 SSE 流连接到 MCP 服务',
    httpType: 'HTTP/HTTPS',
    httpDescription: '通过 HTTP 请求连接到 MCP 服务',
  },

  // AI Agent Pod 管理页面
  agentPods: {
    title: 'AI Agent Pod 管理',
    createPod: '创建 Pod',
    podList: 'Pod 列表',
    name: '名称',
    namespace: '命名空间',
    version: '版本',
    workspace: '工作空间',
    workspacePath: '工作空间路径',
    workspacePathHelp: '容器内工作空间的路径，例如：/workspace',
    workspaceFiles: '工作空间文件',
    browse: '浏览',
    currentPath: '当前路径',
    fileName: '文件名',
    fileSize: '文件大小',
    modTime: '修改时间',
    goUp: '返回上级',
    restarts: '重启次数',
    age: '运行时间',
    node: '节点',
    resources: '资源使用',
    cpu: 'CPU',
    memory: '内存',
    image: '镜像',
    created: '创建时间',
    containers: '容器',
    labels: '标签',
    lastHeartbeat: '最后心跳',
    
    // Pod Types
    cloudPod: '云端 Pod',
    selfRegistered: '自注册 Pod',
    
    // Status
    status: {
      running: '运行中',
      pending: '等待中',
      failed: '失败',
      succeeded: '成功',
      offline: '离线',
    },
    
    // Actions
    viewDetails: '查看详情',
    restart: '重启',
    confirmDelete: '确认删除',
    deleteWarning: '此操作将永久删除该 Pod，无法恢复。',
    
    // Create Pod Form
    podName: 'Pod 名称',
    enterPodName: '请输入 Pod 名称',
    containerImage: '容器镜像',
    selectImage: '选择容器镜像',
    replicas: '副本数',
    resourceRequests: '资源请求',
    resourceLimits: '资源限制',
    cpuRequest: 'CPU 请求',
    memoryRequest: '内存请求',
    cpuLimit: 'CPU 限制',
    memoryLimit: '内存限制',
    
    // Pod Details
    podDetails: 'Pod 详情',
    ready: '就绪',
    notReady: '未就绪',
    
    // Statistics
    totalPods: '总 Pod 数',
    runningPods: '运行中的 Pod',
    pendingPods: '等待中的 Pod',
    failedPods: '失败的 Pod',
    offlinePods: '离线的 Pod',
    cloudPods: '云端 Pod',
    
    // Self-registered agent setup
    selfRegisteredInfo: '自注册 Agent 设置',
    selfRegisteredDescription: '在您自己的电脑上运行 Agent，连接到平台并成为可用的计算资源。',
    step1Title: '下载 Agent',
    step1Description: '下载适用于您操作系统的 Agent 程序。',
    downloadAgent: '下载 Agent',
    step2Title: '安装并运行',
    step2Description: '按照以下步骤安装并运行 Agent：',
    step3Title: '验证连接',
    step3Description: 'Agent 运行后，它将自动连接到平台并出现在您的 Pod 列表中。',
    viewDocs: '查看文档',
    
    // Messages
    createSuccess: 'Pod 创建成功',
    deleteSuccess: 'Pod 删除成功',
    restartSuccess: 'Pod 重启成功',
    createError: '创建 Pod 失败',
    deleteError: '删除 Pod 失败',
    restartError: '重启 Pod 失败',
    fetchError: '获取 Pod 列表失败',
  },

  // 表单验证
  validation: {
    required: '此字段为必填项',
    email: '请输入有效的邮箱地址',
    password: '密码至少需要8个字符',
    confirmPassword: '两次输入的密码不一致',
    minLength: '至少需要 {min} 个字符',
    maxLength: '最多只能输入 {max} 个字符',
    number: '请输入有效的数字',
    positive: '请输入正数',
    url: '请输入有效的URL地址',
  },
};

export default translation; 