const translation = {
  // 通用
  common: {
    loading: 'Loading...',
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    create: 'Create',
    search: 'Search',
    submit: 'Submit',
    back: 'Back',
    success: 'Success',
    deleteSuccess: 'Delete successful',
    error: 'Error',
    warning: 'Warning',
    info: 'Information',
    menu: 'Menu',
    copied: 'Copied to clipboard',
    titleUpdated: 'Title updated',
    copy: 'Copy',
    refresh: 'Refresh',
    close: 'Close',
    next: 'Next',
    previous: 'Previous',
    reset: 'Reset',
    clear: 'Clear',
    enabled: 'Enabled',
    disabled: 'Disabled',
    download: 'Download',
    upload: 'Upload',
    import: 'Import',
    export: 'Export',
    select: 'Select',
    selectAll: 'Select All',
    unselectAll: 'Unselect All',
    expand: 'Expand',
    collapse: 'Collapse',
    total: 'Total',
    noData: 'No Data',
    operation: 'Operation',
    name: 'Name',
    description: 'Description',
    status: 'Status',
    type: 'Type',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    actions: 'Actions',
    pagination: 'Showing {start}-{end} of {total} items',
  },

  // 导航栏
  navbar: {
    title: 'Animus',
    chat: 'Chat',
    apiKeys: 'API Keys',
    llmConfig: 'LLM Config',
    mcpServers: 'MCP Services',
    agentPods: 'Agent Pods',
    credits: 'Credits',
    documentation: 'Documentation',
    logout: 'Logout',
    showNavigation: 'Show Navigation',
  },

  // 错误信息
  errors: {
    generic: 'An error occurred',
    network: 'Network error, please check your connection',
    request: 'Error creating request. Please try again.',
    unknown: 'Unknown error',
    unauthorized: 'Unauthorized, please login again',
    forbidden: 'Forbidden, insufficient permissions',
    notFound: 'Resource not found',
    conversationNotFound: 'Conversation not found',
    badRequest: 'Bad request parameters',
    paymentRequired: 'Payment required to continue.',
    insufficientCredits: 'Insufficient credits',
    conflict: 'Data conflict',
    tooManyRequests: 'Too many requests. Please try again later.',
    serverError: 'Server error, please try again later',
    titleUpdateFailed: 'Failed to update title. Please try again.',
    validationFailed: 'Validation failed',
    timeout: 'Request timeout',
  },

  // 登录页面
  login: {
    title: 'Login',
    welcomeBack: 'Welcome Back',
    pleaseSignIn: 'Please sign in to your account',
    loginWithGitHub: 'Login with GitHub',
    loginWithGoogle: 'Login with Google',
    loginButton: 'Login',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed',
    processing: 'Processing login request...',
    redirecting: 'Login process error, returning to home page...',
  },

  // API密钥页面
  apiKeys: {
    title: 'API Keys',
    createNewKey: 'Create New Key',
    createNew: 'Create New Key',
    keyName: 'Key Name',
    name: 'Name',
    key: 'Key',
    keyNamePlaceholder: 'Enter key name',
    enterName: 'Enter key name',
    keyCreated: 'API key created',
    keyDeleted: 'API key deleted',
    keyCreateError: 'Failed to create API key',
    createError: 'Failed to create API key',
    createSuccess: 'API key created successfully',
    keyDeleteError: 'Failed to delete API key',
    deleteError: 'Failed to delete API key',
    deleteSuccess: 'API key deleted successfully',
    fetchError: 'Failed to fetch data',
    confirmDelete: 'Are you sure you want to delete this API key?',
    deleteConfirm: 'Are you sure you want to delete this API key?',
    copyKey: 'Copy Key',
    copySuccess: 'Copied successfully',
    keyCopied: 'Key copied',
    lastUsed: 'Last Used',
    created: 'Created',
    totalRequests: 'Total Requests',
    requests: 'Requests',
    tokens: 'Tokens',
    actions: 'Actions',
    createdAt: 'Created At',
    never: 'Never',
    keyValue: 'Key Value',
    keyStats: 'Key Statistics',
    statsTitle: 'Usage Statistics',
    loadingStats: 'Loading statistics...',
    totalTokens: 'Total Tokens',
    usageDistribution: 'Usage Distribution',
    tokensDistribution: 'Tokens Distribution',
    dailyUsage: 'Daily Usage',
    dailyTokensUsage: 'Daily Tokens Usage',
    noStats: 'No statistics available',
    totalApiKeys: 'Total API Keys',
    averageRequestsPerKey: 'Average Requests',
    keyNameRequired: 'Please enter key name',
    nameRequired: 'Please enter key name',
  },

  // 积分页面
  credits: {
    title: 'Credits',
    balance: 'Balance',
    currentBalance: 'Current Balance',
    topUp: 'Top Up',
    addCredits: 'Add Credits',
    history: 'Transaction History',
    amount: 'Amount',
    description: 'Description',
    date: 'Date',
    status: 'Status',
    selectAmount: 'Select Amount',
    customAmount: 'Custom Amount',
    enterAmount: 'Enter amount',
    amountRequired: 'Please enter amount',
    amountPositive: 'Amount must be greater than 0',
    paymentMethod: 'Payment Method',
    paymentProvider: 'Payment Provider',
    confirmTopUp: 'Confirm Top Up',
    topUpSuccess: 'Top up successful',
    topUpError: 'Top up failed',
    transaction: 'Transaction',
    transactionId: 'Transaction ID',
    transactionType: 'Transaction Type',
    transactionAmount: 'Transaction Amount',
    transactionDate: 'Transaction Date',
    transactionStatus: 'Transaction Status',
    noTransactions: 'No transactions',
    pending: 'Pending',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
    topup: 'Top Up',
    consume: 'Consume',
    refund: 'Refund',
    bonus: 'Bonus',
    insufficient: 'Insufficient Balance',
    minimum: 'Minimum',
    maximum: 'Maximum',
    provider: 'Provider',
    selectProvider: 'Select Payment Provider',
    providerRequired: 'Please select payment provider',
    processing: 'Processing...',
    redirecting: 'Redirecting...',
    paymentUrl: 'Payment URL',
    openPayment: 'Open Payment Page',
    checkStatus: 'Check Status',
    paymentCompleted: 'Payment Completed',
    paymentFailed: 'Payment Failed',
    paymentCancelled: 'Payment Cancelled',
    invalidAmount: 'Please enter a valid amount',
    minAmount: 'Minimum top-up amount is {amount} credits',
    maxAmount: 'Maximum top-up amount is {amount} credits',
  },

  // 聊天页面
  chat: {
    title: 'Chat',
    newChat: 'New Chat',
    startNewConversation: 'Start a new conversation',
    send: 'Send',
    typeMessage: 'Type a message...',
    askSomething: 'Ask something? You can quote tools, files, resources via @...',
    conversationTitle: 'Conversation title',
    emptyState: 'Start a new conversation',
    conversationCleared: 'Conversation cleared',
    conversationSaved: 'Conversation saved',
    conversationDownloaded: 'Conversation downloaded',
    cannotSaveEmpty: 'Cannot save empty conversation',
    messageEmpty: 'Message cannot be empty',
    responseLoading: 'Generating response...',
    currentModel: 'Current model',
    selectProvider: 'Select model',
    noFileSelected: 'No file selected',
    fileSent: 'File sent',
    imageReceived: 'Image received',
    fileReceived: 'File received',
    uploadFile: 'Upload File',
    clickOrDragFile: 'Click or drag file to this area to upload',
    supportedFileTypes: 'Support for image and document files',
    addMessageWithFile: 'Add message with file (optional)',
    aiResponding: 'AI is responding...',
    interrupt: 'Interrupt',
    statusReady: 'Ready',
    statusThinking: 'Thinking',
    statusRunning: 'Running',
    statusFailed: 'Failed',
  },

  // LLM API 配置页面
  llmConfig: {
    title: 'LLM Providers Configuration',
    addProvider: 'Add Provider',
    editProvider: 'Edit Provider',
    deleteProvider: 'Delete Provider',
    deleteConfirm: 'Are you sure you want to delete this provider?',
    providerName: 'Provider Name',
    providerAlias: 'Alias',
    providerType: 'Provider Type',
    apiType: 'API Type',
    apiKey: 'API Key',
    baseURL: 'Base URL',
    model: 'Model',
    temperature: 'Temperature',
    maxTokens: 'Max Tokens',
    topP: 'Top P',
    frequencyPenalty: 'Frequency Penalty',
    presencePenalty: 'Presence Penalty',
    save: 'Save',
    cancel: 'Cancel',
    required: 'This field is required',
    validationError: 'Please fix the errors before submitting',
    saved: 'Saved successfully',
    deleted: 'Deleted successfully',
    defaultProvider: 'Default Provider',
    setAsDefault: 'Set as Default',
    advanced: 'Advanced Options',
    testConnection: 'Test Connection',
    testSuccess: 'Connection test successful',
    testFailed: 'Connection test failed',
    noProviders: 'No providers',
    addFirst: 'Add your first provider',
    selectProvider: 'Select a provider to use in chat',
    defaultLabel: 'Default',
    manageProviders: 'Manage Providers',
  },

  // MCP Services页面
  mcpServers: {
    title: 'MCP Services',
    addServer: 'Add Service',
    editServer: 'Edit Service',
    name: 'Name',
    namePlaceholder: 'Enter service name',
    nameRequired: 'Please enter service name',
    description: 'Description',
    descriptionPlaceholder: 'Enter service description (optional)',
    type: 'Connection Type',
    typePlaceholder: 'Select connection type',
    typeRequired: 'Please select connection type',
    status: 'Status',
    updatedAt: 'Updated At',
    formMode: 'Form Mode',
    jsonMode: 'JSON Mode',
    
    // StdIO Configuration
    command: 'Command',
    commandPlaceholder: 'Enter execution command',
    commandRequired: 'Please enter execution command',
    args: 'Arguments',
    argsPlaceholder: 'Enter command arguments',
    envVars: 'Environment Variables',
    envVarsPlaceholder: 'KEY1=value1\nKEY2=value2\n...',
    
    // SSE Configuration
    url: 'URL',
    urlPlaceholder: 'Enter SSE service URL',
    urlRequired: 'Please enter URL',
    urlInvalid: 'Please enter a valid URL',
    sseUrlPlaceholder: 'http://localhost:3000/sse',
    headers: 'Headers',
    headersPlaceholder: 'Authorization: Bearer token\nContent-Type: application/json\n...',
    
    // HTTP Configuration
    baseUrl: 'Base URL',
    baseUrlPlaceholder: 'Enter HTTP service base URL',
    baseUrlRequired: 'Please enter base URL',
    baseUrlInvalid: 'Please enter a valid base URL',
    httpBaseUrlPlaceholder: 'http://localhost:3000',
    timeout: 'Timeout',
    timeoutPlaceholder: '30',
    retryCount: 'Retry Count',
    retryCountPlaceholder: '3',
    seconds: 'seconds',
    
    // JSON Editor
    jsonEditorHelp: 'Here you can directly edit the JSON configuration. Please ensure the JSON format is correct.',
    jsonParseError: 'JSON format error, please check syntax',
    jsonValid: 'JSON format is correct',
    validateJson: 'Validate JSON',
    
    // Messages
    createSuccess: 'MCP service created successfully',
    updateSuccess: 'MCP service updated successfully',
    deleteSuccess: 'MCP service deleted successfully',
    toggleSuccess: 'MCP service status updated successfully',
    createError: 'Failed to create MCP service',
    updateError: 'Failed to update MCP service',
    deleteError: 'Failed to delete MCP service',
    toggleError: 'Failed to update MCP service status',
    deleteConfirm: 'Are you sure you want to delete this MCP service?',
    loadTypesError: 'Failed to load service types',
    
    // Types
    stdioType: 'Standard I/O',
    stdioDescription: 'Connect to MCP service via standard input/output',
    sseType: 'Server-Sent Events',
    sseDescription: 'Connect to MCP service via SSE stream',
    httpType: 'HTTP/HTTPS',
    httpDescription: 'Connect to MCP service via HTTP requests',
  },

  // AI Agent Pod Management
  agentPods: {
    title: 'AI Agent Pod Management',
    createPod: 'Create Pod',
    podList: 'Pod List',
    name: 'Name',
    namespace: 'Namespace',
    version: 'Version',
    workspace: 'Workspace',
    workspacePath: 'Workspace Path',
    workspacePathHelp: 'Path to the workspace directory in the container, e.g.: /workspace',
    workspaceFiles: 'Workspace Files',
    browse: 'Browse',
    currentPath: 'Current Path',
    fileName: 'File Name',
    fileSize: 'File Size',
    modTime: 'Modified Time',
    goUp: 'Go Up',
    restarts: 'Restarts',
    age: 'Age',
    node: 'Node',
    resources: 'Resources',
    cpu: 'CPU',
    memory: 'Memory',
    image: 'Image',
    created: 'Created',
    containers: 'Containers',
    labels: 'Labels',
    lastHeartbeat: 'Last Heartbeat',
    
    // Pod Types
    cloudPod: 'Cloud Pod',
    selfRegistered: 'Self-Registered Pod',
    
    // Status
    status: {
      running: 'Running',
      pending: 'Pending',
      failed: 'Failed',
      succeeded: 'Succeeded',
      offline: 'Offline',
    },
    
    // Actions
    viewDetails: 'View Details',
    restart: 'Restart',
    confirmDelete: 'Confirm Delete',
    deleteWarning: 'This action will permanently delete this Pod and cannot be undone.',
    
    // Create Pod Form
    podName: 'Pod Name',
    enterPodName: 'Enter pod name',
    containerImage: 'Container Image',
    selectImage: 'Select container image',
    replicas: 'Replicas',
    resourceRequests: 'Resource Requests',
    resourceLimits: 'Resource Limits',
    cpuRequest: 'CPU Request',
    memoryRequest: 'Memory Request',
    cpuLimit: 'CPU Limit',
    memoryLimit: 'Memory Limit',
    
    // Pod Details
    podDetails: 'Pod Details',
    ready: 'Ready',
    notReady: 'Not Ready',
    
    // Statistics
    totalPods: 'Total Pods',
    runningPods: 'Running Pods',
    pendingPods: 'Pending Pods',
    failedPods: 'Failed Pods',
    offlinePods: 'Offline Pods',
    cloudPods: 'Cloud Pods',
    
    // Self-registered agent setup
    selfRegisteredInfo: 'Self-Registered Agent Setup',
    selfRegisteredDescription: 'Run an Agent on your own computer, connect to the platform and become an available compute resource.',
    step1Title: 'Download Agent',
    step1Description: 'Download the Agent program for your operating system.',
    downloadAgent: 'Download Agent',
    step2Title: 'Install and Run',
    step2Description: 'Follow these steps to install and run the Agent:',
    step3Title: 'Verify Connection',
    step3Description: 'After the Agent is running, it will automatically connect to the platform and appear in your Pod list.',
    viewDocs: 'View Documentation',
    
    // Messages
    createSuccess: 'Pod created successfully',
    deleteSuccess: 'Pod deleted successfully',
    restartSuccess: 'Pod restarted successfully',
    createError: 'Failed to create pod',
    deleteError: 'Failed to delete pod',
    restartError: 'Failed to restart pod',
    fetchError: 'Failed to fetch pod list',
  },

  // Form Validation
  validation: {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    password: 'Password must be at least 8 characters',
    confirmPassword: 'Passwords do not match',
    minLength: 'Must be at least {min} characters',
    maxLength: 'Must be no more than {max} characters',
    number: 'Please enter a valid number',
    positive: 'Please enter a positive number',
    url: 'Please enter a valid URL',
  },
};

export default translation; 