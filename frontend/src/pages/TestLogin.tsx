import { useAuth } from '../hooks/useAuth';
import { Button, Typography, Card, Space, Divider, Alert, Row, Col } from 'antd';
import { useState, useEffect } from 'react';
import axios from 'axios';

const { Title, Text } = Typography;

const TestLogin = () => {
  const { login, user } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [debugError, setDebugError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogin = () => {
    console.log('手动触发登录');
    login();
  };

  const handleDebugAuth = async () => {
    setLoading(true);
    setDebugError(null);
    
    try {
      // 获取本地存储的token
      const token = localStorage.getItem('auth_token');
      
      if (token) {
        // 设置Authorization头
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      }
      
      // 调用调试API
      const response = await axios.get('/api/v1/debug/auth');
      console.log('调试信息:', response.data);
      setDebugInfo(response.data);
    } catch (error) {
      console.error('调试API调用失败:', error);
      setDebugError('调试API调用失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setLoading(false);
    }
  };

  // 自动检查本地存储的token
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    console.log('本地存储的token:', token ? `${token.substring(0, 10)}...` : '无');
  }, []);

  // 响应式布局的宽度
  const getContainerWidth = () => {
    if (windowWidth < 576) return '95%'; // 手机
    if (windowWidth < 992) return '80%'; // 平板
    return '600px'; // 桌面
  };

  return (
    <Row justify="center">
      <Col span={24} style={{ maxWidth: getContainerWidth(), margin: '40px auto' }}>
        <Card bordered={false} style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.08)' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Title level={2} style={{ margin: '0 0 8px' }}>测试登录</Title>
            <Text>当前登录状态: {user ? '已登录' : '未登录'}</Text>
            
            <Button type="primary" size="large" block={windowWidth < 576} onClick={handleLogin}>
              点击登录
            </Button>
            
            <Divider>调试信息</Divider>
            
            <div>
              <Text>本地存储Token: </Text>
              <pre style={{ 
                background: '#f0f0f0', 
                padding: '10px', 
                borderRadius: '4px', 
                maxHeight: '100px', 
                overflow: 'auto',
                fontSize: windowWidth < 576 ? '12px' : '14px',
                wordBreak: 'break-all'
              }}>
                {localStorage.getItem('auth_token') || '无'}
              </pre>
            </div>
            
            <Button 
              type="default" 
              onClick={handleDebugAuth} 
              loading={loading}
              block={windowWidth < 576}
            >
              测试认证
            </Button>
            
            {debugError && (
              <Alert message="错误" description={debugError} type="error" showIcon />
            )}
            
            {debugInfo && (
              <div>
                <Text>调试结果:</Text>
                <pre style={{ 
                  background: '#f0f0f0', 
                  padding: '10px', 
                  borderRadius: '4px',
                  fontSize: windowWidth < 576 ? '12px' : '14px',
                  overflow: 'auto',
                  wordBreak: 'break-all'
                }}>
                  {JSON.stringify(debugInfo, null, 2)}
                </pre>
              </div>
            )}
            
            <div>
              <Text>登录配置信息:</Text>
              <pre style={{
                background: '#f0f0f0',
                padding: '10px',
                borderRadius: '4px',
                fontSize: windowWidth < 576 ? '12px' : '14px',
                overflow: 'auto'
              }}>
                {JSON.stringify({
                  note: '认证配置由后端管理',
                  loginEndpoint: '/api/v1/login',
                  callbackEndpoint: '/api/v1/login/callback',
                  redirectUri: `${window.location.origin}/api/v1/login/callback`
                }, null, 2)}
              </pre>
            </div>
          </Space>
        </Card>
      </Col>
    </Row>
  );
};

export default TestLogin; 