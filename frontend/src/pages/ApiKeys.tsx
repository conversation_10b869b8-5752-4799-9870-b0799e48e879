import { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, message, Popconfirm, Card, Row, Col, Statistic, Divider } from 'antd';
import { PlusOutlined, DeleteOutlined, CopyOutlined } from '@ant-design/icons';
import axios from 'axios';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { useTranslation } from 'react-i18next';

interface ApiKey {
  id: string;
  name: string;
  key: string;
  created_at: string;
  last_used: string | null;
  requests: number;
  tokens: number;
  isEnabled: boolean;
}

interface KeyStat {
  id: string;
  name: string;
  requests: number;
  tokens: number;
  percentage: number;
}

interface DailyUsage {
  date: string;
  requests: number;
  tokens: number;
}

interface ApiKeyStats {
  totalRequests: number;
  totalTokens: number;
  keyStats: KeyStat[];
  dailyUsage: DailyUsage[];
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658'];

const ApiKeys = () => {
  const { t } = useTranslation();
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [stats, setStats] = useState<ApiKeyStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);

  const fetchApiKeys = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/v1/apikeys', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      setApiKeys(response.data.apiKeys || []);
    } catch (error) {
      console.error('Error fetching API keys:', error);
      message.error(t('apiKeys.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  const fetchApiKeyStats = async () => {
    setStatsLoading(true);
    try {
      const response = await axios.get('/api/v1/api-keys/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      setStats(response.data);
    } catch (error) {
      console.error('Error fetching API key stats:', error);
      message.error(t('apiKeys.fetchError'));
    } finally {
      setStatsLoading(false);
    }
  };

  useEffect(() => {
    fetchApiKeys();
    fetchApiKeyStats();
  }, []);

  const handleCreate = async (values: { name: string }) => {
    try {
      const response = await axios.post('/api/v1/api-keys', values, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      setApiKeys((prev) => [...prev, response.data.apiKey]);
      message.success(t('apiKeys.createSuccess'));
      setModalVisible(false);
      form.resetFields();
      // Refresh stats after creating a new key
      fetchApiKeyStats();
    } catch (error) {
      console.error('Error creating API key:', error);
      message.error(t('apiKeys.createError'));
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await axios.delete(`/api/v1/api-keys/${id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      setApiKeys((prev) => prev.filter((key) => key.id !== id));
      message.success(t('apiKeys.deleteSuccess'));
      // Refresh stats after deleting a key
      fetchApiKeyStats();
    } catch (error) {
      console.error('Error deleting API key:', error);
      message.error(t('apiKeys.deleteError'));
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    message.success(t('apiKeys.copySuccess'));
  };

  const columns = [
    {
      title: t('apiKeys.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('apiKeys.key'),
      dataIndex: 'key',
      key: 'key',
      render: (key: string) => (
        <span>
          {key.slice(0, 8)}...{key.slice(-8)}
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={() => copyToClipboard(key)}
          />
        </span>
      ),
    },
    {
      title: t('apiKeys.created'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: t('apiKeys.lastUsed'),
      dataIndex: 'last_used',
      key: 'last_used',
      render: (date: string | null) => date ? new Date(date).toLocaleDateString() : t('apiKeys.never'),
    },
    {
      title: t('apiKeys.requests'),
      dataIndex: 'requests',
      key: 'requests',
    },
    {
      title: t('apiKeys.tokens'),
      dataIndex: 'tokens',
      key: 'tokens',
    },
    {
      title: t('apiKeys.actions'),
      key: 'actions',
      render: (_: any, record: ApiKey) => (
        <Popconfirm
          title={t('apiKeys.deleteConfirm')}
          onConfirm={() => handleDelete(record.id)}
          okText={t('common.confirm')}
          cancelText={t('common.cancel')}
        >
          <Button type="text" danger icon={<DeleteOutlined />} />
        </Popconfirm>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: '16px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setModalVisible(true)}
        >
          {t('apiKeys.createNew')}
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={apiKeys}
        loading={loading}
        rowKey="id"
      />

      <Divider orientation="left">{t('apiKeys.statsTitle')}</Divider>
      
      {statsLoading ? (
        <div style={{ textAlign: 'center', padding: '20px' }}>{t('apiKeys.loadingStats')}</div>
      ) : stats ? (
        <div>
          <Row gutter={16} style={{ marginBottom: '24px' }}>
            <Col xs={24} md={12}>
              <Card>
                <Statistic 
                  title={t('apiKeys.totalRequests')}
                  value={stats.totalRequests} 
                  suffix={t('apiKeys.requests')} 
                />
              </Card>
            </Col>
            <Col xs={24} md={12}>
              <Card>
                <Statistic 
                  title={t('apiKeys.totalTokens')}
                  value={stats.totalTokens} 
                  suffix={t('apiKeys.tokens')} 
                />
              </Card>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col xs={24} lg={12}>
              <Card title={t('apiKeys.usageDistribution')}>
                <div style={{ width: '100%', height: 300 }}>
                  <ResponsiveContainer>
                    <PieChart>
                      <Pie
                        data={stats.keyStats}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="requests"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {stats.keyStats.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${value} ${t('apiKeys.requests')}`} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title={t('apiKeys.tokensDistribution')}>
                <div style={{ width: '100%', height: 300 }}>
                  <ResponsiveContainer>
                    <PieChart>
                      <Pie
                        data={stats.keyStats}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="tokens"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {stats.keyStats.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[(index + 2) % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${value} ${t('apiKeys.tokens')}`} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </Card>
            </Col>
          </Row>

          <Row gutter={16} style={{ marginTop: '16px' }}>
            <Col xs={24} lg={12}>
              <Card title={t('apiKeys.dailyUsage')}>
                <div style={{ width: '100%', height: 300 }}>
                  <ResponsiveContainer>
                    <BarChart data={stats.dailyUsage}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="requests" fill="#82ca9d" name={t('apiKeys.requests')} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title={t('apiKeys.dailyTokensUsage')}>
                <div style={{ width: '100%', height: 300 }}>
                  <ResponsiveContainer>
                    <BarChart data={stats.dailyUsage}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="tokens" fill="#8884d8" name={t('apiKeys.tokens')} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      ) : (
        <div style={{ textAlign: 'center', padding: '20px' }}>{t('apiKeys.noStats')}</div>
      )}

      <Modal
        title={t('apiKeys.createNew')}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form form={form} onFinish={handleCreate}>
          <Form.Item
            name="name"
            label={t('apiKeys.name')}
            rules={[{ required: true, message: t('apiKeys.nameRequired') }]}
          >
            <Input placeholder={t('apiKeys.enterName')} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              {t('common.create')}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ApiKeys; 