import React, { useState, useEffect } from 'react';
import {
  Layout,
  Table,
  Button,
  Card,
  Space,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Descriptions,
  Tooltip,
  Progress,
  Popconfirm,
  message as antMessage,
  Row,
  Col,
  Statistic,
  Badge,
  Steps,
  Tabs,
  Tree,
  Divider,
  Alert,
  Upload
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  SettingOutlined,
  ApiOutlined,
  CloudServerOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  FolderOutlined,
  FileOutlined,
  DownloadOutlined,
  CodeOutlined,
  CloudOutlined,
  DesktopOutlined,
  InfoCircleOutlined,
  BookOutlined,
  LinkOutlined,
  WifiOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { handleApiError, showSuccess, showWarning } from '../utils/errorHandler';

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Step } = Steps;
const { TabPane } = Tabs;
const { TreeNode } = Tree;

interface AgentPod {
  id: string;
  name: string;
  type: 'cloud' | 'self-registered';
  namespace?: string;
  status: 'running' | 'pending' | 'failed' | 'succeeded' | 'offline';
  restarts: number;
  image?: string;
  node?: string;
  workspace_path?: string;
  agent_version?: string;
  last_heartbeat?: string;
  labels: Record<string, string>;
  resources: {
    cpu_request?: string;
    memory_request?: string;
    cpu_limit?: string;
    memory_limit?: string;
  };
  created_at: string;
  updated_at: string;
}

interface WorkspaceFile {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size: number;
  mod_time: string;
  is_executable: boolean;
}

interface CreatePodRequest {
  name: string;
  type: 'cloud' | 'self-registered';
  namespace?: string;
  image?: string;
  replicas?: number;
  workspace_path?: string;
  resources?: {
    cpu_request?: string;
    memory_request?: string;
    cpu_limit?: string;
    memory_limit?: string;
  };
  labels?: Record<string, string>;
}

interface PodStats {
  total: number;
  running: number;
  pending: number;
  failed: number;
  offline: number;
  cloud_pods: number;
  self_registered: number;
}

const AgentPods: React.FC = () => {
  const { t } = useTranslation();
  const [pods, setPods] = useState<AgentPod[]>([]);
  const [stats, setStats] = useState<PodStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [workspaceModalVisible, setWorkspaceModalVisible] = useState(false);
  const [selectedPod, setSelectedPod] = useState<AgentPod | null>(null);
  const [workspaceFiles, setWorkspaceFiles] = useState<WorkspaceFile[]>([]);
  const [currentPath, setCurrentPath] = useState('.');
  const [workspaceLoading, setWorkspaceLoading] = useState(false);
  const [createForm] = Form.useForm();
  const [refreshInterval, setRefreshInterval] = useState<number | null>(null);
  const [createType, setCreateType] = useState<'cloud' | 'self-registered'>('cloud');

  useEffect(() => {
    fetchPods();
    fetchStats();
    
    // Auto refresh every 30 seconds
    const interval = setInterval(() => {
      fetchPods();
      fetchStats();
    }, 30000);
    setRefreshInterval(interval);
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, []);

  const fetchPods = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/v1/agent-pods', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      
      if (response.data && response.data.success) {
        setPods(Array.isArray(response.data.data) ? response.data.data : []);
      } else if (Array.isArray(response.data)) {
        setPods(response.data);
      } else {
        console.warn('Invalid data format received:', response.data);
        setPods([]);
      }
    } catch (error) {
      console.error('Error fetching pods:', error);
      setPods([]);
      handleApiError(error, t);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/v1/agent-pods/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      
      if (response.data && response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching pod stats:', error);
    }
  };

  const fetchWorkspaceFiles = async (podId: string, path: string = '.') => {
    setWorkspaceLoading(true);
    try {
      const response = await axios.get(`/api/v1/agent-pods/${podId}/workspace`, {
        params: { path },
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      
      if (response.data && response.data.success) {
        setWorkspaceFiles(response.data.data || []);
        setCurrentPath(path);
      }
    } catch (error) {
      console.error('Error fetching workspace files:', error);
      handleApiError(error, t);
    } finally {
      setWorkspaceLoading(false);
    }
  };

  const createPod = async (values: CreatePodRequest) => {
    try {
      await axios.post('/api/v1/agent-pods', values, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      showSuccess(t('agentPods.createSuccess'));
      setCreateModalVisible(false);
      createForm.resetFields();
      fetchPods();
      fetchStats();
    } catch (error) {
      handleApiError(error, t);
    }
  };

  const deletePod = async (podId: string) => {
    try {
      await axios.delete(`/api/v1/agent-pods/${podId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      showSuccess(t('agentPods.deleteSuccess'));
      fetchPods();
      fetchStats();
    } catch (error) {
      handleApiError(error, t);
    }
  };

  const restartPod = async (podId: string) => {
    try {
      await axios.post(`/api/v1/agent-pods/${podId}/restart`, {}, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      showSuccess(t('agentPods.restartSuccess'));
      fetchPods();
    } catch (error) {
      handleApiError(error, t);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      case 'succeeded': return 'success';
      case 'offline': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <CheckCircleOutlined />;
      case 'pending': return <SyncOutlined spin />;
      case 'failed': return <CloseCircleOutlined />;
      case 'succeeded': return <CheckCircleOutlined />;
      case 'offline': return <WifiOutlined />;
      default: return <ExclamationCircleOutlined />;
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'cloud' ? <CloudOutlined /> : <DesktopOutlined />;
  };

  const getTypeColor = (type: string) => {
    return type === 'cloud' ? 'blue' : 'green';
  };

  const calculateAge = (createdAt: string) => {
    const now = new Date();
    const created = new Date(createdAt);
    const diffMs = now.getTime() - created.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffDays > 0) return `${diffDays}d`;
    if (diffHours > 0) return `${diffHours}h`;
    return `${diffMins}m`;
  };

  const columns = [
    {
      title: t('agentPods.name'),
      dataIndex: 'name',
      key: 'name',
      width: 300,
      render: (text: string, record: AgentPod) => (
        <Space>
          {getTypeIcon(record.type)}
          <Text strong>{text}</Text>
          <Tag color={getTypeColor(record.type)}>
            {record.type === 'cloud' ? t('agentPods.cloudPod') : t('agentPods.selfRegistered')}
          </Tag>
          {record.namespace && <Tag color="blue">{record.namespace}</Tag>}
        </Space>
      ),
    },
    {
      title: t('common.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {t(`agentPods.status.${status}`)}
        </Tag>
      ),
    },
    {
      title: t('agentPods.version'),
      dataIndex: 'agent_version',
      key: 'agent_version',
      width: 120,
      render: (version: string) => version ? <Text code>{version}</Text> : '-',
    },
    {
      title: t('agentPods.workspace'),
      dataIndex: 'workspace_path',
      key: 'workspace',
      width: 200,
      render: (path: string, record: AgentPod) => (
        <Space>
          {path ? <Text type="secondary">{path}</Text> : '-'}
          {path && (
            <Button
              type="link"
              size="small"
              icon={<FolderOutlined />}
              onClick={() => {
                setSelectedPod(record);
                setWorkspaceModalVisible(true);
                fetchWorkspaceFiles(record.id);
              }}
            >
              {t('agentPods.browse')}
            </Button>
          )}
        </Space>
      ),
    },
    {
      title: t('agentPods.age'),
      dataIndex: 'created_at',
      key: 'age',
      width: 80,
      render: (createdAt: string) => calculateAge(createdAt),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      width: 120,
      render: (record: AgentPod) => (
        <Space>
          <Tooltip title={t('agentPods.viewDetails')}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedPod(record);
                setDetailModalVisible(true);
              }}
            />
          </Tooltip>
          {record.type === 'cloud' && (
            <Tooltip title={t('agentPods.restart')}>
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={() => restartPod(record.id)}
                disabled={record.status === 'pending'}
              />
            </Tooltip>
          )}
          <Popconfirm
            title={t('agentPods.confirmDelete')}
            description={t('agentPods.deleteWarning')}
            onConfirm={() => deletePod(record.id)}
            okText={t('common.confirm')}
            cancelText={t('common.cancel')}
          >
            <Tooltip title={t('common.delete')}>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const renderCreateCloudPodForm = () => (
    <Form
      form={createForm}
      layout="vertical"
      onFinish={(values) => createPod({ ...values, type: 'cloud' })}
      initialValues={{
        namespace: 'default',
        replicas: 1,
        resources: {
          cpu_request: '100m',
          memory_request: '256Mi',
          cpu_limit: '500m',
          memory_limit: '512Mi',
        }
      }}
    >
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="name"
            label={t('agentPods.podName')}
            rules={[{ required: true, message: t('validation.required') }]}
          >
            <Input placeholder={t('agentPods.enterPodName')} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="namespace"
            label={t('agentPods.namespace')}
            rules={[{ required: true, message: t('validation.required') }]}
          >
            <Select>
              <Option value="default">default</Option>
              <Option value="ai-agents">ai-agents</Option>
              <Option value="production">production</Option>
              <Option value="development">development</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="image"
        label={t('agentPods.containerImage')}
        rules={[{ required: true, message: t('validation.required') }]}
      >
        <Select
          placeholder={t('agentPods.selectImage')}
          showSearch
          optionFilterProp="children"
        >
          <Option value="animus/agent:latest">animus/agent:latest</Option>
          <Option value="animus/agent:v1.0.0">animus/agent:v1.0.0</Option>
          <Option value="animus/agent:dev">animus/agent:dev</Option>
          <Option value="openai/gpt-4:latest">openai/gpt-4:latest</Option>
          <Option value="anthropic/claude:latest">anthropic/claude:latest</Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="workspace_path"
        label={t('agentPods.workspacePath')}
        help={t('agentPods.workspacePathHelp')}
      >
        <Input placeholder="/workspace" />
      </Form.Item>

      <Card size="small" title={t('agentPods.resourceLimits')} style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name={['resources', 'cpu_request']}
              label={t('agentPods.cpuRequest')}
            >
              <Input placeholder="100m" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['resources', 'memory_request']}
              label={t('agentPods.memoryRequest')}
            >
              <Input placeholder="256Mi" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['resources', 'cpu_limit']}
              label={t('agentPods.cpuLimit')}
            >
              <Input placeholder="500m" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['resources', 'memory_limit']}
              label={t('agentPods.memoryLimit')}
            >
              <Input placeholder="512Mi" />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
        <Button
          onClick={() => {
            setCreateModalVisible(false);
            createForm.resetFields();
          }}
        >
          {t('common.cancel')}
        </Button>
        <Button type="primary" htmlType="submit">
          {t('common.create')}
        </Button>
      </div>
    </Form>
  );

  const renderSelfRegisteredGuide = () => (
    <div>
      <Alert
        message={t('agentPods.selfRegisteredInfo')}
        description={t('agentPods.selfRegisteredDescription')}
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />
      
      <Steps direction="vertical" size="small" current={-1}>
        <Step
          title={t('agentPods.step1Title')}
          description={
            <div>
              <Paragraph>{t('agentPods.step1Description')}</Paragraph>
              <Button 
                type="primary" 
                icon={<DownloadOutlined />}
                href="/downloads/animus-agent.zip"
                download
              >
                {t('agentPods.downloadAgent')}
              </Button>
            </div>
          }
        />
        <Step
          title={t('agentPods.step2Title')}
          description={
            <div>
              <Paragraph>{t('agentPods.step2Description')}</Paragraph>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px'
              }}>
                {`# Extract the agent
unzip animus-agent.zip
cd animus-agent

# Make it executable
chmod +x animus-agent

# Run the agent
./animus-agent --token YOUR_API_TOKEN --workspace /path/to/workspace`}
              </pre>
            </div>
          }
        />
        <Step
          title={t('agentPods.step3Title')}
          description={
            <Paragraph>
              {t('agentPods.step3Description')}
              <Button type="link" icon={<BookOutlined />} href="/docs/agent-setup" target="_blank">
                {t('agentPods.viewDocs')}
              </Button>
            </Paragraph>
          }
        />
      </Steps>
    </div>
  );

  const renderWorkspaceTree = () => (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center', gap: 8 }}>
        <Text strong>{t('agentPods.currentPath')}: </Text>
        <Text code>{currentPath}</Text>
        {currentPath !== '.' && (
          <Button
            size="small"
            onClick={() => {
              const parentPath = currentPath.split('/').slice(0, -1).join('/') || '.';
              fetchWorkspaceFiles(selectedPod!.id, parentPath);
            }}
          >
            {t('agentPods.goUp')}
          </Button>
        )}
      </div>
      
      <Table
        dataSource={workspaceFiles}
        loading={workspaceLoading}
        size="small"
        pagination={false}
        rowKey="path"
        columns={[
          {
            title: t('agentPods.fileName'),
            dataIndex: 'name',
            key: 'name',
            render: (name: string, file: WorkspaceFile) => (
              <Space>
                {file.type === 'directory' ? <FolderOutlined /> : <FileOutlined />}
                {file.type === 'directory' ? (
                  <Button
                    type="link"
                    onClick={() => fetchWorkspaceFiles(selectedPod!.id, file.path)}
                  >
                    {name}
                  </Button>
                ) : (
                  <Text>{name}</Text>
                )}
                {file.is_executable && <Tag color="orange">exec</Tag>}
              </Space>
            ),
          },
          {
            title: t('agentPods.fileSize'),
            dataIndex: 'size',
            key: 'size',
            render: (size: number, file: WorkspaceFile) => 
              file.type === 'file' ? `${(size / 1024).toFixed(1)} KB` : '-',
          },
          {
            title: t('agentPods.modTime'),
            dataIndex: 'mod_time',
            key: 'mod_time',
            render: (time: string) => new Date(time).toLocaleString(),
          },
        ]}
      />
    </div>
  );

  return (
    <Layout style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      <Content style={{ padding: '24px' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '24px' 
          }}>
            <Title level={2} style={{ margin: 0 }}>
              <CloudServerOutlined style={{ marginRight: '12px', color: '#1890ff' }} />
              {t('agentPods.title')}
            </Title>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  fetchPods();
                  fetchStats();
                }}
                loading={loading}
              >
                {t('common.refresh')}
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateModalVisible(true)}
              >
                {t('agentPods.createPod')}
              </Button>
            </Space>
          </div>

          {/* Statistics Cards */}
          {stats && (
            <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
              <Col xs={24} sm={8} lg={4}>
                <Card>
                  <Statistic
                    title={t('agentPods.totalPods')}
                    value={stats.total}
                    prefix={<CloudServerOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={8} lg={4}>
                <Card>
                  <Statistic
                    title={t('agentPods.runningPods')}
                    value={stats.running}
                    prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={8} lg={4}>
                <Card>
                  <Statistic
                    title={t('agentPods.cloudPods')}
                    value={stats.cloud_pods}
                    prefix={<CloudOutlined style={{ color: '#1890ff' }} />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={8} lg={4}>
                <Card>
                  <Statistic
                    title={t('agentPods.selfRegistered')}
                    value={stats.self_registered}
                    prefix={<DesktopOutlined style={{ color: '#52c41a' }} />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={8} lg={4}>
                <Card>
                  <Statistic
                    title={t('agentPods.pendingPods')}
                    value={stats.pending}
                    prefix={<SyncOutlined style={{ color: '#faad14' }} />}
                    valueStyle={{ color: '#faad14' }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={8} lg={4}>
                <Card>
                  <Statistic
                    title={t('agentPods.offlinePods')}
                    value={stats.offline}
                    prefix={<WifiOutlined style={{ color: '#ff4d4f' }} />}
                    valueStyle={{ color: '#ff4d4f' }}
                  />
                </Card>
              </Col>
            </Row>
          )}

          {/* Pods Table */}
          <Card
            title={
              <Space>
                <ApiOutlined />
                {t('agentPods.podList')}
              </Space>
            }
            style={{ borderRadius: '8px' }}
          >
            <Table
              columns={columns}
              dataSource={pods}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  t('common.pagination', { start: range[0], end: range[1], total }),
              }}
              scroll={{ x: 1000 }}
            />
          </Card>
        </div>

        {/* Create Pod Modal */}
        <Modal
          title={
            <Space>
              <PlusOutlined />
              {t('agentPods.createPod')}
            </Space>
          }
          open={createModalVisible}
          onCancel={() => {
            setCreateModalVisible(false);
            createForm.resetFields();
          }}
          footer={null}
          width={800}
        >
          <Tabs activeKey={createType} onChange={(key) => setCreateType(key as 'cloud' | 'self-registered')}>
            <TabPane
              tab={
                <Space>
                  <CloudOutlined />
                  {t('agentPods.cloudPod')}
                </Space>
              }
              key="cloud"
            >
              {renderCreateCloudPodForm()}
            </TabPane>
            <TabPane
              tab={
                <Space>
                  <DesktopOutlined />
                  {t('agentPods.selfRegistered')}
                </Space>
              }
              key="self-registered"
            >
              {renderSelfRegisteredGuide()}
            </TabPane>
          </Tabs>
        </Modal>

        {/* Pod Detail Modal */}
        <Modal
          title={
            <Space>
              <EyeOutlined />
              {t('agentPods.podDetails')}
            </Space>
          }
          open={detailModalVisible}
          onCancel={() => setDetailModalVisible(false)}
          footer={null}
          width={800}
        >
          {selectedPod && (
            <div>
              <Descriptions bordered column={2} style={{ marginBottom: 16 }}>
                <Descriptions.Item label={t('agentPods.name')} span={2}>
                  <Space>
                    {getTypeIcon(selectedPod.type)}
                    <Text strong>{selectedPod.name}</Text>
                    <Tag color={getTypeColor(selectedPod.type)}>
                      {selectedPod.type === 'cloud' ? t('agentPods.cloudPod') : t('agentPods.selfRegistered')}
                    </Tag>
                    {selectedPod.namespace && <Tag color="blue">{selectedPod.namespace}</Tag>}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label={t('agentPods.status')}>
                  <Tag color={getStatusColor(selectedPod.status)} icon={getStatusIcon(selectedPod.status)}>
                    {t(`agentPods.status.${selectedPod.status}`)}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label={t('agentPods.version')}>
                  {selectedPod.agent_version ? <Text code>{selectedPod.agent_version}</Text> : '-'}
                </Descriptions.Item>
                {selectedPod.image && (
                  <Descriptions.Item label={t('agentPods.image')} span={2}>
                    <Text code>{selectedPod.image}</Text>
                  </Descriptions.Item>
                )}
                {selectedPod.workspace_path && (
                  <Descriptions.Item label={t('agentPods.workspacePath')} span={2}>
                    <Text code>{selectedPod.workspace_path}</Text>
                  </Descriptions.Item>
                )}
                <Descriptions.Item label={t('agentPods.created')}>
                  {new Date(selectedPod.created_at).toLocaleString()}
                </Descriptions.Item>
                <Descriptions.Item label={t('agentPods.age')}>
                  {calculateAge(selectedPod.created_at)}
                </Descriptions.Item>
                {selectedPod.last_heartbeat && (
                  <Descriptions.Item label={t('agentPods.lastHeartbeat')} span={2}>
                    {new Date(selectedPod.last_heartbeat).toLocaleString()}
                  </Descriptions.Item>
                )}
              </Descriptions>

              {Object.keys(selectedPod.labels).length > 0 && (
                <Card size="small" title={t('agentPods.labels')} style={{ marginTop: 16 }}>
                  <Space wrap>
                    {Object.entries(selectedPod.labels).map(([key, value]) => (
                      <Tag key={key} color="blue">
                        {key}={value}
                      </Tag>
                    ))}
                  </Space>
                </Card>
              )}
            </div>
          )}
        </Modal>

        {/* Workspace Files Modal */}
        <Modal
          title={
            <Space>
              <FolderOutlined />
              {t('agentPods.workspaceFiles')}
              {selectedPod && <Text type="secondary">- {selectedPod.name}</Text>}
            </Space>
          }
          open={workspaceModalVisible}
          onCancel={() => setWorkspaceModalVisible(false)}
          footer={null}
          width={900}
        >
          {renderWorkspaceTree()}
        </Modal>
      </Content>
    </Layout>
  );
};

export default AgentPods; 