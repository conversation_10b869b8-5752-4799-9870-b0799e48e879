import { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, Form, InputNumber, message, Typography, Select } from 'antd';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import { handleApiError } from '../utils/errorHandler';

const { Title } = Typography;
const { Option } = Select;

interface Transaction {
  id: string;
  amount: number;
  description: string;
  date: string;
  credits: number;
  status: string;
}

interface PaymentProvider {
  name: string;
  display_name: string;
  supported_methods: string[];
  enabled: boolean;
}

interface PaymentConfig {
  conversion_rate: number;
  min_amount: number;
  max_amount: number;
  supported_currencies: string[];
  default_provider: string;
}

const Credits = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [balance, setBalance] = useState(0);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [paymentProviders, setPaymentProviders] = useState<PaymentProvider[]>([]);
  const [paymentConfig, setPaymentConfig] = useState<PaymentConfig | null>(null);
  const [form] = Form.useForm();

  // Load initial data
  useEffect(() => {
    refreshData();
    loadPaymentProviders();
    loadPaymentConfig();
  }, []);

  // Load payment providers
  const loadPaymentProviders = async () => {
    try {
      const response = await axios.get('/api/v1/payments/providers');
      if (response.data.success) {
        setPaymentProviders(response.data.providers);
      }
    } catch (error) {
      console.error('Failed to load payment providers:', error);
    }
  };

  // Load payment configuration
  const loadPaymentConfig = async () => {
    try {
      const response = await axios.get('/api/v1/payments/config');
      if (response.data.success) {
        setPaymentConfig(response.data.config);
      }
    } catch (error) {
      console.error('Failed to load payment config:', error);
    }
  };

  const fetchBalance = async () => {
    try {
      const response = await axios.get('/api/v1/balance', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      setBalance(response.data.balance?.credits || 0);
    } catch (error) {
      handleApiError(error, t);
    }
  };

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/v1/transactions', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      setTransactions(response.data.transactions || []);
    } catch (error) {
      handleApiError(error, t);
    } finally {
      setLoading(false);
    }
  };

  const handleTopUp = async (values: { amount: number; provider: string }) => {
    try {
      setLoading(true);

      if (!paymentConfig) {
        message.error('Payment configuration not loaded');
        return;
      }

      // Validate amount
      if (values.amount < paymentConfig.min_amount || values.amount > paymentConfig.max_amount) {
        message.error(`Amount must be between ${paymentConfig.min_amount} and ${paymentConfig.max_amount}`);
        return;
      }

      // Create payment order
      const response = await axios.post('/api/v1/payments', {
        amount: values.amount,
        description: `Top-up ${values.amount * paymentConfig.conversion_rate} credits`,
        provider: values.provider || paymentConfig.default_provider,
        payment_type: 'alipay'
      });

      if (response.data.success) {
        // Redirect to payment URL
        const paymentURL = response.data.pay_url;
        if (paymentURL) {
          window.open(paymentURL, '_blank');
          message.success('Payment order created successfully. Please complete the payment in the new tab.');
          
          // Start polling for payment status
          pollPaymentStatus(response.data.order_id, values.provider || paymentConfig.default_provider);
        } else {
          message.error('No payment URL received');
        }
        
        setModalVisible(false);
        form.resetFields();
      } else {
        message.error('Failed to create payment order');
      }
    } catch (error) {
      console.error('Top-up error:', error);
      handleApiError(error, t);
    } finally {
      setLoading(false);
    }
  };

  // Poll payment status
  const pollPaymentStatus = async (orderId: string, provider: string) => {
    const checkPayment = async () => {
      try {
        const response = await axios.get(`/api/v1/payments/${orderId}?provider=${provider}`);
        if (response.data.success) {
          const status = response.data.status;
          
          if (status === 'success') {
            message.success('Payment completed successfully!');
            refreshData(); // Refresh balance and transactions
            return true; // Stop polling
          } else if (status === 'failed' || status === 'cancelled') {
            message.error(`Payment ${status}`);
            return true; // Stop polling
          }
          // Continue polling if status is 'pending'
        }
      } catch (error) {
        console.error('Error checking payment status:', error);
      }
      return false; // Continue polling
    };

    // Poll every 3 seconds for up to 5 minutes
    const maxAttempts = 100;
    let attempts = 0;
    
    const pollInterval = setInterval(async () => {
      attempts++;
      const shouldStop = await checkPayment();
      
      if (shouldStop || attempts >= maxAttempts) {
        clearInterval(pollInterval);
        if (attempts >= maxAttempts) {
          message.info('Payment status check timeout. Please refresh to see the latest status.');
        }
      }
    }, 3000);
  };

  const refreshData = async () => {
    await Promise.all([
      fetchBalance(),
      fetchTransactions()
    ]);
  };

  const columns = [
    {
      title: t('credits.amount'),
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: amount >= 0 ? 'green' : 'red' }}>
          {amount}
        </span>
      ),
    },
    {
      title: t('credits.description'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('credits.title'),
      dataIndex: 'credits',
      key: 'credits',
      render: (credits: number) => credits || 0,
    },
    {
      title: t('credits.status'),
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: t('credits.date'),
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => new Date(date).toLocaleString(),
      sorter: (a: Transaction, b: Transaction) => {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      },
      defaultSortOrder: 'descend' as const,
    },
  ];

  return (
    <div>
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4}>{t('credits.currentBalance')}: {balance} {t('credits.title')}</Title>
          <div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisible(true);
                // Set default provider when modal opens
                setTimeout(() => {
                  if (paymentConfig && paymentProviders.length > 0) {
                    form.setFieldsValue({
                      provider: paymentConfig.default_provider
                    });
                  }
                }, 100);
              }}
              style={{ marginRight: '8px' }}
            >
              {t('credits.addCredits')}
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={refreshData}
            >
              {t('common.refresh')}
            </Button>
          </div>
        </div>
      </Card>

      <Table
        columns={columns}
        dataSource={transactions}
        loading={loading}
        rowKey="id"
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={t('credits.addCredits')}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form form={form} onFinish={handleTopUp}>
          <Form.Item
            name="amount"
            label={t('credits.amount')}
            rules={[
              { required: true, message: t('credits.amountRequired') },
              { type: 'number', min: 1, message: t('credits.amountPositive') },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder={t('credits.enterAmount')}
            />
          </Form.Item>
          <Form.Item
            name="provider"
            label={t('credits.paymentProvider')}
            rules={[
              { required: true, message: t('credits.providerRequired') },
            ]}
          >
            <Select
              style={{ width: '100%' }}
              placeholder={t('credits.selectProvider')}
            >
              {paymentProviders.map((provider) => (
                <Option key={provider.name} value={provider.name}>
                  {provider.display_name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              {t('credits.addCredits')}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Credits; 