import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const Callback = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [status, setStatus] = useState(t('login.processing'));

  useEffect(() => {
    console.log("Callback component mounted");
    // 检查URL是否有token参数
    const params = new URLSearchParams(window.location.search);
    const token = params.get('token');
    
    if (token) {
      // 我们已经有token了，说明这是后端回调处理完成后的重定向
      console.log("Received token from backend redirect, saving to localStorage");
      localStorage.setItem('auth_token', token);
      
      // 清除URL中的token参数
      window.history.replaceState({}, document.title, '/');
      
      // 重定向到首页
      console.log("Redirecting to home page");
      setStatus(t('login.loginSuccess'));
      navigate('/', { replace: true });
      return;
    }
    
    // 如果没有token，但有code参数，说明这是直接访问/callback页面
    // 这不应该发生，因为我们的回调地址是/api/v1/login/callback
    // 为防止循环，我们直接跳转到首页
    console.log("No token in URL, redirecting to home page");
    setStatus(t('login.redirecting'));
    navigate('/', { replace: true });
  }, [navigate, t]);

  return (
    <div style={{textAlign:'center', marginTop:'20vh'}}>
      <h2>{status}</h2>
    </div>
  );
};

export default Callback; 