import { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Space,
  message,
  Typography,
  Tabs,
  Popconfirm,
  Tag,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CodeOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import { handleApiError } from '../utils/errorHandler';
import MonacoEditor from '@monaco-editor/react';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface MCPServer {
  id: string;
  name: string;
  description: string;
  type: 'stdio' | 'sse' | 'http';
  config: Record<string, any>;
  is_enabled: boolean;
  created_at: string;
  updated_at: string;
}

interface MCPServerType {
  value: string;
  label: string;
  description: string;
}

const MCPServers = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [serverTypes, setServerTypes] = useState<MCPServerType[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingServer, setEditingServer] = useState<MCPServer | null>(null);
  const [editMode, setEditMode] = useState<'form' | 'json'>('form');
  const [form] = Form.useForm();

  // Load initial data
  useEffect(() => {
    loadServers();
    loadServerTypes();
  }, []);

  const loadServers = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/v1/mcp-servers', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      
      // 处理不同的响应格式
      let serverData = [];
      if (response.data) {
        if (response.data.success && response.data.data) {
          serverData = response.data.data;
        } else if (Array.isArray(response.data)) {
          serverData = response.data;
        } else if (response.data.data) {
          serverData = response.data.data;
        }
      }
      
      console.log('Loaded MCP servers:', serverData);
      setServers(serverData || []);
    } catch (error) {
      console.error('Error loading MCP servers:', error);
      handleApiError(error, t);
    } finally {
      setLoading(false);
    }
  };

  const loadServerTypes = async () => {
    try {
      const response = await axios.get('/api/v1/mcp-servers/types', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      
      // 处理不同的响应格式
      let typesData = [];
      if (response.data) {
        if (response.data.success && response.data.data) {
          typesData = response.data.data;
        } else if (Array.isArray(response.data)) {
          typesData = response.data;
        } else if (response.data.data) {
          typesData = response.data.data;
        }
      }
      
      console.log('Loaded server types:', typesData);
      setServerTypes(typesData || []);
    } catch (error) {
      console.error(t('mcpServers.loadTypesError'), error);
    }
  };

  const handleCreate = () => {
    setEditingServer(null);
    setEditMode('form');
    form.resetFields();
    form.setFieldsValue({
      type: 'stdio',
      config: getDefaultConfig('stdio'),
    });
    setModalVisible(true);
  };

  const handleEdit = (server: MCPServer) => {
    setEditingServer(server);
    setEditMode('form');
    form.setFieldsValue({
      name: server.name,
      description: server.description,
      type: server.type,
      config: server.config,
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await axios.delete(`/api/v1/mcp-servers/${id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      message.success(t('mcpServers.deleteSuccess'));
      loadServers();
    } catch (error) {
      handleApiError(error, t);
    }
  };

  const handleToggle = async (id: string, enabled: boolean) => {
    try {
      await axios.put(`/api/v1/mcp-servers/${id}/toggle`, {
        is_enabled: enabled,
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      message.success(t('mcpServers.toggleSuccess'));
      loadServers();
    } catch (error) {
      handleApiError(error, t);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const config = {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        }
      };
      
      if (editingServer) {
        await axios.put(`/api/v1/mcp-servers/${editingServer.id}`, values, config);
        message.success(t('mcpServers.updateSuccess'));
      } else {
        await axios.post('/api/v1/mcp-servers', values, config);
        message.success(t('mcpServers.createSuccess'));
      }
      setModalVisible(false);
      loadServers();
    } catch (error) {
      handleApiError(error, t);
    }
  };

  // 获取默认配置模板 - 这些是用户配置的起始模板，用户需要根据实际情况修改
  const getDefaultConfig = (type: string) => {
    switch (type) {
      case 'stdio':
        return {
          command: ['node'],
          args: ['server.js'],
          env: {},
        };
      case 'sse':
        return {
          url: 'http://localhost:3000/sse', // 用户需要修改为实际的 SSE 服务地址
          headers: {},
        };
      case 'http':
        return {
          base_url: 'http://localhost:3000', // 用户需要修改为实际的 HTTP 服务地址
          timeout: 30,
          retry_count: 3,
        };
      default:
        return {};
    }
  };

  const renderConfigForm = (type: string) => {
    switch (type) {
      case 'stdio':
        return (
          <>
            <Form.Item
              label={t('mcpServers.command')}
              name={['config', 'command']}
              rules={[{ required: true, message: t('mcpServers.commandRequired') }]}
            >
              <Select mode="tags" placeholder={t('mcpServers.commandPlaceholder')}>
                <Option value="node">node</Option>
                <Option value="python">python</Option>
                <Option value="python3">python3</Option>
              </Select>
            </Form.Item>
            <Form.Item
              label={t('mcpServers.args')}
              name={['config', 'args']}
            >
              <Select mode="tags" placeholder={t('mcpServers.argsPlaceholder')} />
            </Form.Item>
            <Form.Item
              label={t('mcpServers.envVars')}
              name={['config', 'env']}
            >
              <Input.TextArea
                rows={3}
                placeholder={t('mcpServers.envVarsPlaceholder')}
                style={{ fontFamily: 'monospace' }}
              />
            </Form.Item>
          </>
        );
      case 'sse':
        return (
          <>
            <Form.Item
              label={t('mcpServers.url')}
              name={['config', 'url']}
              rules={[
                { required: true, message: t('mcpServers.urlRequired') },
                { type: 'url', message: t('mcpServers.urlInvalid') },
              ]}
            >
              <Input placeholder={t('mcpServers.sseUrlPlaceholder')} />
            </Form.Item>
            <Form.Item
              label={t('mcpServers.headers')}
              name={['config', 'headers']}
            >
              <Input.TextArea
                rows={3}
                placeholder={t('mcpServers.headersPlaceholder')}
                style={{ fontFamily: 'monospace' }}
              />
            </Form.Item>
          </>
        );
      case 'http':
        return (
          <>
            <Form.Item
              label={t('mcpServers.baseUrl')}
              name={['config', 'base_url']}
              rules={[
                { required: true, message: t('mcpServers.baseUrlRequired') },
                { type: 'url', message: t('mcpServers.baseUrlInvalid') },
              ]}
            >
              <Input placeholder={t('mcpServers.httpBaseUrlPlaceholder')} />
            </Form.Item>
            <Form.Item
              label={t('mcpServers.timeout')}
              name={['config', 'timeout']}
            >
              <Input type="number" placeholder={t('mcpServers.timeoutPlaceholder')} addonAfter={t('mcpServers.seconds')} />
            </Form.Item>
            <Form.Item
              label={t('mcpServers.retryCount')}
              name={['config', 'retry_count']}
            >
              <Input type="number" placeholder={t('mcpServers.retryCountPlaceholder')} />
            </Form.Item>
            <Form.Item
              label={t('mcpServers.headers')}
              name={['config', 'headers']}
            >
              <Input.TextArea
                rows={3}
                placeholder={t('mcpServers.headersPlaceholder')}
                style={{ fontFamily: 'monospace' }}
              />
            </Form.Item>
          </>
        );
      default:
        return null;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'stdio':
        return 'blue';
      case 'sse':
        return 'green';
      case 'http':
        return 'orange';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: t('mcpServers.name'),
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (name: string, record: MCPServer) => (
        <div>
          <div style={{ fontWeight: 500 }}>{name}</div>
          {record.description && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.description}
            </Text>
          )}
        </div>
      ),
    },
    {
      title: t('mcpServers.type'),
      dataIndex: 'type',
      key: 'type',
      width: 140,
      render: (type: string) => {
        const typeMap = {
          'stdio': t('mcpServers.stdioType'),
          'sse': t('mcpServers.sseType'),
          'http': t('mcpServers.httpType')
        };
        return (
          <Tag color={getTypeColor(type)}>
            {typeMap[type as keyof typeof typeMap] || type.toUpperCase()}
          </Tag>
        );
      },
    },
    {
      title: t('mcpServers.status'),
      dataIndex: 'is_enabled',
      key: 'is_enabled',
      width: 120,
      render: (enabled: boolean, record: MCPServer) => (
        <Switch
          checked={enabled}
          onChange={(checked) => handleToggle(record.id, checked)}
          checkedChildren={t('common.enabled')}
          unCheckedChildren={t('common.disabled')}
          size="small"
        />
      ),
    },
    {
      title: t('mcpServers.updatedAt'),
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 160,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      width: 100,
      render: (_: any, record: MCPServer) => (
        <Space size="small">
          <Tooltip title={t('common.edit')}>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title={t('mcpServers.deleteConfirm')}
            onConfirm={() => handleDelete(record.id)}
            okText={t('common.confirm')}
            cancelText={t('common.cancel')}
          >
            <Tooltip title={t('common.delete')}>
              <Button type="text" size="small" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
          <Title level={4}>{t('mcpServers.title')}</Title>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              {t('mcpServers.addServer')}
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadServers}
            >
              {t('common.refresh')}
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={servers}
          loading={loading}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>

      <Modal
        title={editingServer ? t('mcpServers.editServer') : t('mcpServers.addServer')}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Tabs
          activeKey={editMode}
          onChange={(key) => setEditMode(key as 'form' | 'json')}
          items={[
            {
              key: 'form',
              label: (
                <span>
                  <SettingOutlined />
                  {t('mcpServers.formMode')}
                </span>
              ),
              children: (
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleSubmit}
                  initialValues={{
                    type: 'stdio',
                    config: getDefaultConfig('stdio'),
                  }}
                >
                  <Form.Item
                    label={t('mcpServers.name')}
                    name="name"
                    rules={[{ required: true, message: t('mcpServers.nameRequired') }]}
                  >
                    <Input placeholder={t('mcpServers.namePlaceholder')} />
                  </Form.Item>

                  <Form.Item
                    label={t('mcpServers.description')}
                    name="description"
                  >
                    <Input.TextArea
                      rows={2}
                      placeholder={t('mcpServers.descriptionPlaceholder')}
                    />
                  </Form.Item>

                  <Form.Item
                    label={t('mcpServers.type')}
                    name="type"
                    rules={[{ required: true, message: t('mcpServers.typeRequired') }]}
                  >
                    <Select
                      placeholder={t('mcpServers.typePlaceholder')}
                      onChange={(value) => {
                        form.setFieldsValue({
                          config: getDefaultConfig(value),
                        });
                      }}
                    >
                      {serverTypes.map((type) => (
                        <Option key={type.value} value={type.value}>
                          <div>
                            <div>{type.label}</div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {type.description}
                            </Text>
                          </div>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item noStyle shouldUpdate={(prev, curr) => prev.type !== curr.type}>
                    {({ getFieldValue }) => {
                      const type = getFieldValue('type');
                      return renderConfigForm(type);
                    }}
                  </Form.Item>

                  <Form.Item>
                    <Space>
                      <Button type="primary" htmlType="submit">
                        {editingServer ? t('common.save') : t('common.create')}
                      </Button>
                      <Button onClick={() => setModalVisible(false)}>
                        {t('common.cancel')}
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              ),
            },
            {
              key: 'json',
              label: (
                <span>
                  <CodeOutlined />
                  {t('mcpServers.jsonMode')}
                </span>
              ),
              children: (
                <JSONConfigEditor
                  initialValue={editingServer || { name: '', description: '', type: 'stdio', config: getDefaultConfig('stdio') }}
                  onSave={handleSubmit}
                  onCancel={() => setModalVisible(false)}
                />
              ),
            },
          ]}
        />
      </Modal>
    </div>
  );
};

// JSON Configuration Editor Component
interface JSONConfigEditorProps {
  initialValue: any;
  onSave: (data: any) => void;
  onCancel: () => void;
}

const JSONConfigEditor: React.FC<JSONConfigEditorProps> = ({
  initialValue,
  onSave,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [jsonValue, setJsonValue] = useState(JSON.stringify(initialValue, null, 2));
  const [error, setError] = useState<string | null>(null);

  const handleSave = () => {
    try {
      const parsed = JSON.parse(jsonValue);
      setError(null);
      onSave(parsed);
    } catch (err) {
      setError(t('mcpServers.jsonParseError'));
    }
  };

  const handleValidate = () => {
    try {
      JSON.parse(jsonValue);
      setError(null);
      message.success(t('mcpServers.jsonValid'));
    } catch (err) {
      setError(t('mcpServers.jsonParseError'));
    }
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">{t('mcpServers.jsonEditorHelp')}</Text>
      </div>
      
      <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, overflow: 'hidden' }}>
        <MonacoEditor
          height="400px"
          language="json"
          value={jsonValue}
          onChange={(value: string | undefined) => setJsonValue(value || '')}
          options={{
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            fontSize: 14,
            wordWrap: 'on',
            automaticLayout: true,
          }}
        />
      </div>

      {error && (
        <div style={{ color: '#ff4d4f', marginTop: 8, fontSize: '14px' }}>
          {error}
        </div>
      )}

      <div style={{ marginTop: 16, textAlign: 'right' }}>
        <Space>
          <Button onClick={handleValidate}>
            {t('mcpServers.validateJson')}
          </Button>
          <Button onClick={onCancel}>
            {t('common.cancel')}
          </Button>
          <Button type="primary" onClick={handleSave} disabled={!!error}>
            {t('common.save')}
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default MCPServers; 