import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Card, 
  Modal, 
  Form, 
  Input, 
  InputNumber, 
  Select, 
  Switch, 
  Tooltip, 
  Space, 
  Typography, 
  Divider, 
  Collapse, 
  Empty, 
  message,
  Tag
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  StarOutlined, 
  StarFilled, 
  QuestionCircleOutlined,
  <PERSON>boltOutlined
} from '@ant-design/icons';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import { handleApiError, showSuccess } from '../utils/errorHandler';

const { Title, Text } = Typography;
const { Panel } = Collapse;
const { Option } = Select;

// LLM Provider types based on litellm
const providerTypes = [
  { value: 'openai', label: 'OpenAI' },
  { value: 'azure', label: 'Azure OpenAI' },
  { value: 'anthropic', label: 'Anthropic' },
  { value: 'cohere', label: 'Cohere' },
  { value: 'replicate', label: 'Replicate' },
  { value: 'huggingface', label: 'Hugging Face' },
  { value: 'google', label: 'Google (Gemini)' },
  { value: 'mistral', label: 'Mistral AI' },
  { value: 'custom', label: 'Custom API' }
];

// Model templates for different providers
const defaultModels: Record<string, string[]> = {
  openai: ['gpt-4o', 'gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo'],
  azure: ['gpt-4o', 'gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo'],
  anthropic: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku', 'claude-2.1'],
  cohere: ['command', 'command-light', 'command-r', 'command-r-plus'],
  replicate: ['replicate/llama-3-70b-chat', 'replicate/llama-2-70b-chat'],
  huggingface: ['meta-llama/Llama-2-70b-chat-hf', 'tiiuae/falcon-180b'],
  google: ['gemini-pro', 'gemini-ultra'],
  mistral: ['mistral-large', 'mistral-medium', 'mistral-small'],
  custom: []
};

interface LLMProvider {
  id: string;
  name: string;
  alias: string;
  type: string;
  apiKey: string;
  baseURL?: string;
  model: string;
  isDefault: boolean;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  isBuiltIn?: boolean;
}

const LLMConfig: React.FC = () => {
  const { t } = useTranslation();
  const [providers, setProviders] = useState<LLMProvider[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingProvider, setEditingProvider] = useState<LLMProvider | null>(null);
  const [testingConnection, setTestingConnection] = useState(false);
  const [advancedVisible, setAdvancedVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchProviders();
  }, []);

  const fetchProviders = async () => {
    setLoading(true);
    try {
      // 并行获取用户配置的提供商和系统内置提供商
      const [userProvidersResponse, systemProvidersResponse] = await Promise.all([
        axios.get('/api/v1/llm-providers', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          },
        }),
        axios.get('/api/v1/llm-providers/system', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          },
        })
      ]);
      
      const userProviders = userProvidersResponse.data || [];
      const systemProviders = (systemProvidersResponse.data || []).map((provider: any) => ({
        ...provider,
        isBuiltIn: true // 标记为系统内置提供商
      }));
      
      // 系统提供商优先显示，然后是用户提供商
      const allProviders = [...systemProviders, ...userProviders];
      setProviders(allProviders);
    } catch (error) {
      handleApiError(error, t);
    } finally {
      setLoading(false);
    }
  };

  const openModal = (provider?: LLMProvider) => {
    setEditingProvider(provider || null);
    form.resetFields();
    
    if (provider) {
      form.setFieldsValue({
        ...provider
      });
      setAdvancedVisible(
        provider.temperature !== undefined || 
        provider.maxTokens !== undefined || 
        provider.topP !== undefined || 
        provider.frequencyPenalty !== undefined || 
        provider.presencePenalty !== undefined
      );
    } else {
      setAdvancedVisible(false);
    }
    
    setModalVisible(true);
  };

  const handleSave = async (values: any) => {
    try {
      const providerData = {
        ...values,
        id: editingProvider?.id
      };
      
      const endpoint = editingProvider 
        ? `/api/v1/llm-providers/${editingProvider.id}` 
        : '/api/v1/llm-providers';
      
      const method = editingProvider ? 'put' : 'post';
      
      const response = await axios({
        method,
        url: endpoint,
        data: providerData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      
      if (method === 'post') {
        setProviders([...providers, response.data]);
      } else if (editingProvider) {
        setProviders(providers.map(p => p.id === editingProvider.id ? response.data : p));
      }
      
      showSuccess(t('llmConfig.saved'));
      setModalVisible(false);
    } catch (error) {
      handleApiError(error, t);
    }
  };

  const handleDelete = (provider: LLMProvider) => {
    Modal.confirm({
      title: t('llmConfig.deleteProvider'),
      content: t('llmConfig.deleteConfirm'),
      onOk: async () => {
        try {
          await axios.delete(`/api/v1/llm-providers/${provider.id}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
            },
          });
          
          setProviders(providers.filter(p => p.id !== provider.id));
          showSuccess(t('llmConfig.deleted'));
        } catch (error) {
          handleApiError(error, t);
        }
      }
    });
  };

  const setAsDefault = async (provider: LLMProvider) => {
    // 防止系统内置提供商被设置为默认
    if (provider.isBuiltIn) {
      message.warning('System providers cannot be set as default');
      return;
    }

    try {
      await axios.put(`/api/v1/llm-providers/${provider.id}/default`, {}, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      
      setProviders(providers.map(p => ({
        ...p,
        isDefault: p.id === provider.id
      })));
      
      showSuccess(t('llmConfig.saved'));
    } catch (error) {
      handleApiError(error, t);
    }
  };

  const testConnection = async () => {
    try {
      setTestingConnection(true);
      const values = await form.validateFields();
      
      await axios.post('/api/v1/llm-providers/test', values, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      
      showSuccess(t('llmConfig.testSuccess'));
    } catch (error) {
      handleApiError(error, t);
    } finally {
      setTestingConnection(false);
    }
  };

  const handleProviderTypeChange = (type: string) => {
    form.setFieldValue('model', '');
  };

  const columns = [
    {
      title: t('llmConfig.defaultProvider'),
      dataIndex: 'isDefault',
      key: 'isDefault',
      width: 80,
      render: (isDefault: boolean, record: LLMProvider) => (
        isDefault ? (
          <StarFilled style={{ color: '#faad14', fontSize: '18px' }} />
        ) : (
          <Button
            type="text"
            icon={<StarOutlined />}
            onClick={() => setAsDefault(record)}
            title={t('llmConfig.setAsDefault')}
            disabled={record.isBuiltIn}
          />
        )
      ),
    },
    {
      title: t('llmConfig.providerType'),
      dataIndex: 'isBuiltIn',
      key: 'providerCategory',
      width: 100,
      render: (isBuiltIn: boolean) => (
        isBuiltIn ? (
          <Tag color="#f0f9ff" style={{ color: '#0ea5e9', fontSize: '12px', border: 'none' }}>
            <ThunderboltOutlined style={{ marginRight: '4px' }} />
            System
          </Tag>
        ) : (
          <Tag color="#f0f7ff" style={{ color: '#6366f1', fontSize: '12px', border: 'none' }}>
            User
          </Tag>
        )
      ),
    },
    {
      title: t('llmConfig.providerName'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('llmConfig.providerAlias'),
      dataIndex: 'alias',
      key: 'alias',
    },
    {
      title: t('llmConfig.apiType'),
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const provider = providerTypes.find(p => p.value === type);
        return provider ? provider.label : type;
      }
    },
    {
      title: t('llmConfig.model'),
      dataIndex: 'model',
      key: 'model',
    },
    {
      title: t('common.actions'),
      key: 'action',
      width: 120,
      render: (_: any, record: LLMProvider) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
            disabled={record.isBuiltIn}
            title={record.isBuiltIn ? 'System providers cannot be edited' : 'Edit provider'}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
            disabled={record.isDefault || record.isBuiltIn}
            title={record.isBuiltIn ? 'System providers cannot be deleted' : (record.isDefault ? 'Default provider cannot be deleted' : 'Delete provider')}
          />
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4}>{t('llmConfig.title')}</Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => openModal()}
          >
            {t('llmConfig.addProvider')}
          </Button>
        </div>
      </Card>

      {providers.length > 0 ? (
        <Table
          columns={columns}
          dataSource={providers}
          rowKey="id"
          loading={loading}
          pagination={false}
        />
      ) : (
        <Card>
          <Empty
            description={
              <Space direction="vertical" align="center">
                <Text>{t('llmConfig.noProviders')}</Text>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => openModal()}
                >
                  {t('llmConfig.addFirst')}
                </Button>
              </Space>
            }
          />
        </Card>
      )}

      <Modal
        title={editingProvider ? t('llmConfig.editProvider') : t('llmConfig.addProvider')}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            type: 'openai',
            model: 'gpt-4o',
            temperature: 0.7,
            maxTokens: 2000,
            topP: 1,
            frequencyPenalty: 0,
            presencePenalty: 0,
            isDefault: false
          }}
        >
          <Form.Item
            name="name"
            label={t('llmConfig.providerName')}
            rules={[{ required: true, message: t('llmConfig.required') }]}
          >
            <Input placeholder="My OpenAI" />
          </Form.Item>

          <Form.Item
            name="alias"
            label={
              <Space>
                {t('llmConfig.providerAlias')}
                <Tooltip title="A short name to identify this provider in the chat interface">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Space>
            }
            rules={[{ required: true, message: t('llmConfig.required') }]}
          >
            <Input placeholder="gpt4" />
          </Form.Item>

          <Form.Item
            name="type"
            label={t('llmConfig.providerType')}
            rules={[{ required: true, message: t('llmConfig.required') }]}
          >
            <Select onChange={handleProviderTypeChange}>
              {providerTypes.map(provider => (
                <Option key={provider.value} value={provider.value}>{provider.label}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="apiKey"
            label={t('llmConfig.apiKey')}
            rules={[{ required: true, message: t('llmConfig.required') }]}
          >
            <Input.Password placeholder="sk-..." />
          </Form.Item>

          <Form.Item
            name="baseURL"
            label={t('llmConfig.baseURL')}
          >
            <Input placeholder="https://api.openai.com/v1" />
          </Form.Item>

          <Form.Item
            name="model"
            label={t('llmConfig.model')}
            rules={[{ required: true, message: t('llmConfig.required') }]}
          >
            <Select
              showSearch
              placeholder="Select a model"
              optionFilterProp="children"
              allowClear
              dropdownRender={menu => (
                <>
                  {menu}
                  <Divider style={{ margin: '8px 0' }} />
                  <Space style={{ padding: '0 8px 4px' }}>
                    <Input.Group compact>
                      <Input 
                        style={{ width: 'calc(100% - 32px)' }} 
                        placeholder="Custom model name"
                        value={form.getFieldValue('model')}
                        onChange={e => form.setFieldValue('model', e.target.value)}
                      />
                    </Input.Group>
                  </Space>
                </>
              )}
            >
              {(defaultModels[form.getFieldValue('type') as string] || []).map((model: string) => (
                <Option key={model} value={model}>{model}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="isDefault"
            valuePropName="checked"
          >
            <Switch checkedChildren={t('llmConfig.defaultProvider')} unCheckedChildren={t('llmConfig.defaultProvider')} />
          </Form.Item>

          <Divider orientation="left">
            <a onClick={() => setAdvancedVisible(!advancedVisible)}>
              {t('llmConfig.advanced')}
            </a>
          </Divider>

          {advancedVisible && (
            <>
              <Form.Item
                name="temperature"
                label={
                  <Space>
                    {t('llmConfig.temperature')}
                    <Tooltip title="Controls randomness: 0.0 is deterministic, 1.0 is most random">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="maxTokens"
                label={
                  <Space>
                    {t('llmConfig.maxTokens')}
                    <Tooltip title="Maximum number of tokens in the response">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="topP"
                label={
                  <Space>
                    {t('llmConfig.topP')}
                    <Tooltip title="Controls diversity via nucleus sampling">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <InputNumber min={0} max={1} step={0.05} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="frequencyPenalty"
                label={
                  <Space>
                    {t('llmConfig.frequencyPenalty')}
                    <Tooltip title="Penalizes repeated tokens">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <InputNumber min={-2} max={2} step={0.1} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="presencePenalty"
                label={
                  <Space>
                    {t('llmConfig.presencePenalty')}
                    <Tooltip title="Penalizes tokens already present in the text">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <InputNumber min={-2} max={2} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </>
          )}

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingProvider ? t('llmConfig.save') : t('llmConfig.addProvider')}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                {t('llmConfig.cancel')}
              </Button>
              <Button 
                type="dashed" 
                icon={<ThunderboltOutlined />} 
                onClick={testConnection}
                loading={testingConnection}
              >
                {t('llmConfig.testConnection')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default LLMConfig; 