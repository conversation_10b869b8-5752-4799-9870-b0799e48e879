import React, { useState, useEffect, useRef } from 'react';
import { 
  Layout, 
  Input, 
  Button, 
  List, 
  Typography, 
  Card, 
  Space, 
  Dropdown, 
  Menu, 
  Avatar, 
  Divider, 
  Empty, 
  Spin, 
  message as antMessage,
  Tooltip,
  Drawer,
  Modal,
  Upload,
  Select,
  Tag
} from 'antd';
import { 
  SendOutlined, 
  RobotOutlined, 
  UserOutlined, 
  DeleteOutlined,
  CopyOutlined,
  Ellip<PERSON>Outlined,
  PlusOutlined,
  LoadingOutlined,
  DownloadOutlined,
  MenuOutlined,
  SaveOutlined,
  EditOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  CloseOutlined,
  PaperClipOutlined,
  FileImageOutlined,
  FileOutlined,
  SettingOutlined,
  GlobalOutlined,
  ArrowUpOutlined,
  DownOutlined,
  ThunderboltOutlined,
  AudioOutlined,
  SearchOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import { handleApiError, showSuccess, showWarning } from '../utils/errorHandler';
import { useNavigate, useParams } from 'react-router-dom';

const { Content, Sider } = Layout;
const { TextArea } = Input;
const { Text, Title, Paragraph } = Typography;
const { Dragger } = Upload;

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  hasAttachment?: boolean;
  attachmentType?: 'image' | 'file';
  attachmentUrl?: string;
  attachmentName?: string;
}

interface Conversation {
  id: string;
  title: string;
  preview: string;
  lastUpdated: Date;
  messages: Message[];
}

interface User {
  id: string;
  email: string;
  name?: string;
}

const Chat: React.FC = () => {
  const { t } = useTranslation();
  const { conversationId } = useParams<{ conversationId?: string }>();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [fetchingConversations, setFetchingConversations] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [currentConversation, setCurrentConversation] = useState('New Conversation');
  const [currentConversationId, setCurrentConversationId] = useState<string>(conversationId || '');
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [editTitleModalVisible, setEditTitleModalVisible] = useState(false);
  const [newTitle, setNewTitle] = useState('');
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [providers, setProviders] = useState<any[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [fetchingProviders, setFetchingProviders] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [headerVisible, setHeaderVisible] = useState(true);
  
  // Live API states
  const [isStreaming, setIsStreaming] = useState(false);
  const [liveStatus, setLiveStatus] = useState<'ready' | 'thinking' | 'running' | 'failed'>('ready');
  const [websocket, setWebsocket] = useState<WebSocket | null>(null);
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);
  
  // 添加强制重渲染状态来确保AI回复显示
  const [forceUpdate, setForceUpdate] = useState(0);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await axios.get('/api/v1/me', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      
      if (response.data) {
        // 从response.data中提取用户信息
        const userData = response.data.user || response.data;
        setUser({
          id: userData.sub || userData.id,
          email: userData.email,
          name: userData.name || userData.given_name
        });
      }
    } catch (error) {
      console.error('Error fetching user info:', error);
      // 如果获取用户信息失败，设置默认值
      setUser({
        id: 'unknown',
        email: '<EMAIL>',
        name: 'User'
      });
    }
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setWindowWidth(width);
      if (width < 768) {
        setSidebarCollapsed(true);
        setSidebarVisible(false);
      }
    };
    
    // 初始化时也调用一次
    handleResize();
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 从后端加载对话历史记录
  useEffect(() => {
    fetchConversations();
    fetchUserInfo();
  }, []);

  // 改进的消息显示和滚动逻辑
  useEffect(() => {
    if (messages.length > 0) {
      console.log('Messages updated, current count:', messages.length);
      console.log('Latest messages:', messages.slice(-2));
      
      // 使用多重确保机制来滚动到底部
      setTimeout(() => scrollToBottom(), 10);
      setTimeout(() => scrollToBottom(), 50);
      setTimeout(() => scrollToBottom(), 200);
      
      // 强制组件重渲染
      setForceUpdate(prev => prev + 1);
    }
  }, [messages, messages.length]);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleUpload = async () => {
    if (fileList.length === 0) {
      showWarning(t('chat.noFileSelected'));
      return;
    }

    setUploadLoading(true);
    try {
      const file = fileList[0];
      const isImage = file.type.startsWith('image/');
      
      // In a real app, you would upload to a server and get a URL back
      // For now, we'll use a local Data URL
      const reader = new FileReader();
      reader.onloadend = () => {
        const url = reader.result as string;
        
        // Create a message with the attachment
        const userMessage: Message = {
          id: Date.now().toString(),
          role: 'user',
          content: input || t('chat.fileSent'),
          timestamp: new Date(),
          hasAttachment: true,
          attachmentType: isImage ? 'image' : 'file',
          attachmentUrl: url,
          attachmentName: file.name
        };

        setMessages(prev => [...prev, userMessage]);
        setInput('');
        setFileList([]);
        setUploadModalVisible(false);
        
        // In a real app, you would also send this to the backend with the selectedProvider
        // For now, we'll just simulate a response
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: isImage 
            ? t('chat.imageReceived') 
            : t('chat.fileReceived'),
          timestamp: new Date()
        };
        
        setTimeout(() => {
          setMessages(prev => [...prev, assistantMessage]);
          scrollToBottom();
        }, 1000);
      };
      
      reader.readAsDataURL(file.originFileObj);
    } catch (error) {
      handleApiError(error, t);
    } finally {
      setUploadLoading(false);
    }
  };

  const fetchConversations = async () => {
    setFetchingConversations(true);
    try {
      console.log('Fetching conversations from server...');
      const response = await axios.get('/api/v1/conversations', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      
      console.log('Received conversations:', response.data);
      
      if (response.data && Array.isArray(response.data)) {
        // 转换日期字符串回Date对象
        const conversationsWithDates = response.data.map((conv: any) => ({
          ...conv,
          lastUpdated: new Date(conv.lastUpdated),
          // 安全地处理消息数据 - 对话列表通常不包含完整消息
          messages: Array.isArray(conv.messages) ? conv.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          })) : []
        }));
        console.log('Processed conversations:', conversationsWithDates);
        setConversations(conversationsWithDates);
      } else {
        console.warn('Invalid conversations data received:', response.data);
        setConversations([]);
      }
    } catch (error) {
      handleApiError(error, t);
    } finally {
      setFetchingConversations(false);
    }
  };

  const isMobile = windowWidth < 768;
  const isTablet = windowWidth >= 768 && windowWidth < 1200;

  const fetchProviders = async () => {
    setFetchingProviders(true);
    try {
      // 并行获取用户配置的提供商和系统内置提供商
      const [userProvidersResponse, systemProvidersResponse] = await Promise.all([
        axios.get('/api/v1/llm-providers', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          }
        }),
        axios.get('/api/v1/llm-providers/system', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          }
        })
      ]);
      
      const userProviders = userProvidersResponse.data || [];
      const systemProviders = (systemProvidersResponse.data || []).map((provider: any) => ({
        ...provider,
        isBuiltIn: true // 标记为系统内置提供商
      }));
      
      // 系统提供商优先显示，然后是用户提供商
      const allProviders = [...systemProviders, ...userProviders];
      setProviders(allProviders);
      
      // Set default provider if available
      const defaultProvider = allProviders.find((p: any) => p.isDefault);
      if (defaultProvider) {
        setSelectedProvider(defaultProvider.id);
      } else if (allProviders.length > 0) {
        setSelectedProvider(allProviders[0].id);
      }
    } catch (error) {
      console.error('Error fetching providers:', error);
      setProviders([]);
      handleApiError(error, t);
    } finally {
      setFetchingProviders(false);
    }
  };

  // Fetch providers when component mounts
  useEffect(() => {
    fetchProviders();
  }, []);

  // Live API WebSocket connection
  const connectLiveAPI = async () => {
    if (websocket) {
      websocket.close();
    }

    try {
      setLiveStatus('ready');
      const token = localStorage.getItem('auth_token');
      
      // 检查是否有token
      if (!token) {
        console.warn('No auth token found, skipping WebSocket connection');
        setLiveStatus('failed');
        return;
      }
      
      // 检查后端是否运行
      try {
        await axios.get('/api/v1/health', {
          headers: { 'Authorization': `Bearer ${token}` },
          timeout: 3000
        });
      } catch (healthError) {
        console.warn('Backend health check failed, WebSocket may not be available:', healthError);
        setLiveStatus('failed');
        return;
      }
      
      // 构建 WebSocket URL，使用当前主机和协议
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const wsUrl = `${protocol}//${host}/api/v1/live?token=${token}`;

      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        console.log('Live API WebSocket connected');
        setWebsocket(ws);
        setLiveStatus('ready');
        
        // Send initial setup
        const setup = {
          setup: {
            model: `models/${providers.find(p => p.id === selectedProvider)?.name || 'gemini-1.5-flash'}`,
            generationConfig: {
              responseModalities: ["TEXT"],
              speechConfig: {
                voiceConfig: { prebuiltVoiceConfig: { voiceName: "Aoede" } }
              }
            }
          }
        };
        ws.send(JSON.stringify(setup));
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleLiveAPIMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('Live API WebSocket disconnected', event.code, event.reason);
        setWebsocket(null);
        setIsStreaming(false);
        setLiveStatus('ready');
      };

      ws.onerror = (error) => {
        console.error('Live API WebSocket error:', error);
        setLiveStatus('failed');
        setWebsocket(null);
      };

    } catch (error) {
      console.error('Error connecting to Live API:', error);
      setLiveStatus('failed');
    }
  };

  // Handle Live API messages
  const handleLiveAPIMessage = (data: any) => {
    console.log('Received Live API message:', data);

    if (data.serverContent) {
      const content = data.serverContent;

      if (content.modelTurn && content.modelTurn.parts) {
        const textPart = content.modelTurn.parts.find((part: any) => part.text);
        if (textPart && textPart.text) {
          console.log('Processing streaming text chunk:', textPart.text);

          setMessages(prev => {
            // 查找最后一条助手消息
            const lastMessage = prev[prev.length - 1];

            // 如果最后一条消息是助手消息且正在流式传输中，则追加内容
            if (lastMessage &&
                lastMessage.role === 'assistant' &&
                (streamingMessageId === null || lastMessage.id === streamingMessageId)) {

              console.log('Appending to existing assistant message:', lastMessage.id);
              const updatedMessages = [...prev];
              updatedMessages[updatedMessages.length - 1] = {
                ...lastMessage,
                content: lastMessage.content + textPart.text
              };

              // 设置流式消息ID（如果还没有设置）
              if (!streamingMessageId) {
                setStreamingMessageId(lastMessage.id);
              }

              return updatedMessages;
            } else {
              // 创建新的助手消息
              const newMessageId = `streaming_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
              console.log('Creating new assistant message:', newMessageId);
              setStreamingMessageId(newMessageId);

              const assistantMessage: Message = {
                id: newMessageId,
                role: 'assistant',
                content: textPart.text,
                timestamp: new Date(),
              };

              return [...prev, assistantMessage];
            }
          });

          // 确保滚动到底部
          setTimeout(() => scrollToBottom(), 10);
        }
      }

      if (content.turnComplete) {
        console.log('Streaming turn completed');
        setIsStreaming(false);
        const currentStreamingId = streamingMessageId;
        setStreamingMessageId(null);
        setLiveStatus('ready');
        scrollToBottom();

        // 保存流式消息到对话中
        if (currentConversationId && currentStreamingId) {
          console.log('Saving streaming message to conversation');
          // 延迟保存，确保消息状态已更新
          setTimeout(() => {
            saveStreamingMessageWithId(currentStreamingId);
          }, 100);
        }

        // 刷新对话列表
        setTimeout(() => {
          fetchConversations();
        }, 1000);
      }
    }

    if (data.toolCall) {
      setLiveStatus('running');
    }

    if (data.error) {
      console.error('Live API error:', data.error);
      setIsStreaming(false);
      setStreamingMessageId(null);
      setLiveStatus('failed');

      // 显示错误消息
      const errorMessage: Message = {
        id: `error_${Date.now()}`,
        role: 'assistant',
        content: `Error: ${data.error.message || 'Unknown error occurred'}`,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  // 保存流式消息到对话
  const saveStreamingMessage = async () => {
    if (!currentConversationId || !streamingMessageId) return;

    try {
      const streamingMessage = messages.find(msg => msg.id === streamingMessageId);
      if (!streamingMessage) return;

      // 保存助手消息到对话
      await axios.post(`/api/v1/conversations/${currentConversationId}/messages`, {
        role: streamingMessage.role,
        content: streamingMessage.content,
        timestamp: streamingMessage.timestamp.toISOString()
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });

      console.log('Streaming message saved to conversation');
    } catch (error) {
      console.error('Error saving streaming message:', error);
    }
  };

  // 保存指定ID的流式消息到对话
  const saveStreamingMessageWithId = async (messageId: string) => {
    if (!currentConversationId || !messageId) return;

    try {
      const streamingMessage = messages.find(msg => msg.id === messageId);
      if (!streamingMessage) {
        console.log('Streaming message not found:', messageId);
        return;
      }

      // 保存助手消息到对话
      await axios.post(`/api/v1/conversations/${currentConversationId}/messages`, {
        role: streamingMessage.role,
        content: streamingMessage.content,
        timestamp: streamingMessage.timestamp.toISOString()
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });

      console.log('Streaming message saved to conversation:', messageId);
    } catch (error) {
      console.error('Error saving streaming message:', error);
    }
  };

  // Send message via Live API
  const sendLiveMessage = async (message: string) => {
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
      console.log('WebSocket not ready, attempting to connect...');
      await connectLiveAPI();
      return;
    }

    console.log('Sending message via Live API:', message);

    // 彻底重置流式状态
    setStreamingMessageId(null);
    setLiveStatus('thinking');
    setIsStreaming(true);

    // 确保没有残留的流式消息状态
    setTimeout(() => {
      console.log('Streaming state reset completed');
    }, 10);

    // Send message via WebSocket
    const clientContent = {
      clientContent: {
        turns: [
          {
            role: "user",
            parts: [{ text: message }]
          }
        ],
        turnComplete: true
      }
    };

    console.log('Sending WebSocket message:', clientContent);
    websocket.send(JSON.stringify(clientContent));
  };

  // Interrupt streaming
  const interruptStreaming = () => {
    if (websocket && isStreaming) {
      // Send interrupt signal
      const interrupt = {
        clientContent: {
          turns: [],
          turnComplete: true
        }
      };
      websocket.send(JSON.stringify(interrupt));
      setIsStreaming(false);
      setStreamingMessageId(null);
      setLiveStatus('ready');
    }
  };

  // Connect to Live API when provider changes - 作为可选功能
  useEffect(() => {
    if (selectedProvider && providers.length > 0) {
      // 延迟连接WebSocket，避免启动时的错误
      const timer = setTimeout(() => {
        connectLiveAPI();
      }, 2000);
      
      return () => {
        clearTimeout(timer);
        if (websocket) {
          websocket.close();
        }
      };
    }
  }, [selectedProvider]);

  const handleSend = async () => {
    if (!input.trim() || loading) return;

    console.log('=== STARTING MESSAGE SEND ===');
    console.log('Current messages count before send:', messages.length);

    // 保存原始输入
    const originalInput = input.trim();
    const sendTimestamp = Date.now();

    setInput('');
    setLoading(true);

    // 创建唯一的用户消息ID
    const userMessage: Message = {
      id: `user_${sendTimestamp}`,
      role: 'user',
      content: originalInput,
      timestamp: new Date(),
    };

    try {
      // 立即添加用户消息并强制更新
      console.log('Adding user message:', userMessage);

      setMessages(prevMessages => {
        const newMessages = [...prevMessages, userMessage];
        console.log('User message added, new count:', newMessages.length);
        return newMessages;
      });

      // 强制重渲染
      setForceUpdate(prev => prev + 1);

      // 确保滚动
      setTimeout(() => scrollToBottom(), 100);

      // 处理对话创建
      let conversationId = currentConversationId;

      if (!conversationId) {
        console.log('Creating new conversation...');
        const firstMessage = originalInput.substring(0, 30) + (originalInput.length > 30 ? '...' : '');

          const createResponse = await axios.post('/api/v1/conversations', {
            title: firstMessage,
          messages: []
          }, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
            }
          });

          conversationId = createResponse.data.id;
          setCurrentConversationId(conversationId);
          setCurrentConversation(createResponse.data.title);
        navigate(`/console/chat/${conversationId}`, { replace: true });

        console.log('New conversation created:', conversationId);
      }

      // 优先使用流式API
      if (websocket && websocket.readyState === WebSocket.OPEN && liveStatus === 'ready') {
        console.log('Using Live API for streaming response');
        await sendLiveMessage(originalInput);
        setLoading(false);
        return;
      } else {
        console.log('WebSocket not ready, falling back to traditional API');
        // 尝试连接WebSocket
        await connectLiveAPI();
        if (websocket && websocket.readyState === WebSocket.OPEN) {
          console.log('WebSocket connected, using Live API');
          await sendLiveMessage(originalInput);
          setLoading(false);
          return;
        }
      }
      
      // 发送API请求
      console.log('Sending API request...');
      console.log('Provider ID:', selectedProvider);
      
      const chatResponse = await axios.post('/api/v1/chat', 
        { 
          message: originalInput,
          conversationId: conversationId,
          providerId: selectedProvider
        },
        { 
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000 // 30秒超时
        }
      );

      console.log('=== API RESPONSE RECEIVED ===');
      console.log('Status:', chatResponse.status);
      console.log('Response data:', chatResponse.data);
      
      // 解析AI回复内容
      let responseContent = '';
      
      if (chatResponse.data) {
        if (typeof chatResponse.data === 'string') {
          responseContent = chatResponse.data;
        } else if (chatResponse.data.response) {
          responseContent = chatResponse.data.response;
        } else if (chatResponse.data.data?.response) {
          responseContent = chatResponse.data.data.response;
        } else if (chatResponse.data.message) {
          responseContent = chatResponse.data.message;
        } else if (chatResponse.data.content) {
          responseContent = chatResponse.data.content;
        } else {
          console.warn('Unexpected response format:', chatResponse.data);
          responseContent = JSON.stringify(chatResponse.data);
        }
      }
      
      if (!responseContent || responseContent.trim() === '') {
        responseContent = "I received your message, but my response was empty. Please try again.";
      }
      
      console.log('Extracted AI response:', responseContent);
      
      // 创建AI回复消息
      const assistantMessage: Message = {
        id: `assistant_${sendTimestamp + 1}`,
        role: 'assistant',
        content: responseContent,
        timestamp: new Date(),
      };

      console.log('=== ADDING AI RESPONSE ===');
      console.log('Assistant message:', assistantMessage);

      // 添加AI回复并确保显示
      setMessages(prevMessages => {
        const newMessages = [...prevMessages, assistantMessage];
        console.log('AI message added, new total count:', newMessages.length);
        console.log('Last 3 messages:', newMessages.slice(-3));
        return newMessages;
      });
      
      // 多重确保机制
      setTimeout(() => {
        setForceUpdate(prev => prev + 1);
        scrollToBottom();
        console.log('Force update and scroll triggered');
      }, 100);
      
      setTimeout(() => {
        scrollToBottom();
        console.log('Additional scroll triggered');
      }, 300);
      
      // 刷新对话列表
      setTimeout(() => {
      fetchConversations();
      }, 1000);
      
      console.log('=== MESSAGE SEND COMPLETED ===');
      
    } catch (error) {
      console.error('=== ERROR IN MESSAGE SEND ===', error);
      
      // 创建错误消息
      const errorMessage: Message = {
        id: `error_${sendTimestamp + 2}`,
        role: 'assistant',
        content: `Sorry, there was an error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
      };
      
      setMessages(prevMessages => {
        const newMessages = [...prevMessages, errorMessage];
        console.log('Error message added, count:', newMessages.length);
        return newMessages;
      });
      
      setTimeout(() => {
        setForceUpdate(prev => prev + 1);
        scrollToBottom();
      }, 100);
      
      const customErrorMap = {
        402: t('errors.insufficientCredits')
      };
      handleApiError(error, t, customErrorMap);
    } finally {
      setLoading(false);
      console.log('Loading state cleared');
    }
  };

  const clearConversation = () => {
    setMessages([]);
    setCurrentConversation('New Conversation');
    setCurrentConversationId('');
    // 导航到新对话页面
    navigate('/console/chat', { replace: true });
    showSuccess(t('chat.conversationCleared'));
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    showSuccess(t('common.copied'));
  };

  const downloadConversation = () => {
    const conversationText = messages
      .map(msg => `${msg.role === 'user' ? 'You' : 'AI'}: ${msg.content}`)
      .join('\n\n');
    
    const blob = new Blob([conversationText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentConversation}-${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showSuccess(t('chat.conversationDownloaded'));
  };

  const saveNewConversation = async () => {
    if (messages.length === 0) {
      showWarning(t('chat.cannotSaveEmpty'));
      return;
    }

    try {
      console.log('Saving new conversation with title:', currentConversation);
      console.log('Messages to save:', messages);
      
      const response = await axios.post('/api/v1/conversations', {
        title: currentConversation,
        messages: messages
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });
      
      console.log('Conversation saved:', response.data);
      setCurrentConversationId(response.data.id);
      fetchConversations();
      showSuccess(t('chat.conversationSaved'));
    } catch (error) {
      handleApiError(error, t);
    }
  };

  const loadConversation = async (conversation: Conversation) => {
    try {
      console.log('Loading conversation:', conversation.id);

      // 如果已经是当前对话，不重复加载
      if (conversation.id === currentConversationId) {
        console.log('Conversation already loaded, skipping');
        return;
      }

      const response = await axios.get(`/api/v1/conversations/${conversation.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });

      console.log('Conversation loaded from server:', response.data);

      // 安全地处理消息数据
      let messagesWithDates: Message[] = [];
      if (response.data && Array.isArray(response.data.messages)) {
        messagesWithDates = response.data.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
      } else {
        console.warn('No messages found in conversation or messages is not an array:', response.data);
        messagesWithDates = [];
      }

      // 一次性更新所有相关状态
      setMessages(messagesWithDates);
      setCurrentConversation(conversation.title);
      setCurrentConversationId(conversation.id);

      // 更新URL
      navigate(`/console/chat/${conversation.id}`, { replace: true });

      // 滚动到底部
      setTimeout(() => scrollToBottom(), 200);

      console.log('Conversation loaded successfully:', {
        id: conversation.id,
        title: conversation.title,
        messageCount: messagesWithDates.length
      });

    } catch (error) {
      console.error('Error loading conversation:', error);
      handleApiError(error, t);
    }
  };

  const deleteConversation = async (id: string, e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    console.log(`Delete conversation button clicked for ID: ${id}`);
    console.log('Current conversations:', conversations);
    
    // 使用antd的Modal.confirm确保确认对话框正确显示
    Modal.confirm({
      title: t('common.confirm') || 'Confirm Deletion',
      content: t('common.warning') || 'Are you sure you want to delete this conversation? This action cannot be undone.',
      okText: t('common.confirm') || 'Delete',
      cancelText: t('common.cancel') || 'Cancel',
      okType: 'danger',
      centered: true,
      onOk: async () => {
        try {
          console.log(`Confirmed deletion. Sending DELETE request to /api/v1/conversations/${id}`);
          
          const token = localStorage.getItem('auth_token');
          console.log('Auth token available:', !!token);
          
          if (!token) {
            console.error('No auth token found');
            showWarning('Authentication token not found. Please refresh and try again.');
            return Promise.reject('No auth token');
          }
          
          const response = await axios.delete(`/api/v1/conversations/${id}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          console.log(`Delete response status: ${response.status}`, response.data);
          
          // 检查响应状态
          if (response.status === 200 || response.status === 204) {
          // 更新本地会话列表
            setConversations(prev => {
              const filtered = prev.filter(c => c.id !== id);
              console.log(`Conversations before delete: ${prev.length}, after delete: ${filtered.length}`);
              return filtered;
            });
          
          // 如果删除的是当前会话，创建新会话
          if (id === currentConversationId) {
              console.log('Deleted conversation was current, clearing...');
            clearConversation();
          }
          
            showSuccess(t('common.deleteSuccess') || 'Conversation deleted successfully');
            
            // 重新获取对话列表以确保数据同步
            setTimeout(() => {
              console.log('Refetching conversations after delete...');
              fetchConversations();
            }, 300);
            
            return Promise.resolve();
          } else {
            console.error('Unexpected response status:', response.status);
            showWarning('Delete operation may not have completed successfully');
            return Promise.reject('Unexpected response status');
          }
        } catch (error) {
          console.error('Error deleting conversation:', error);
          if (axios.isAxiosError(error)) {
            console.error('Response data:', error.response?.data);
            console.error('Response status:', error.response?.status);
            console.error('Request config:', error.config);
            
            if (error.response?.status === 404) {
              console.log('Conversation already deleted, removing from local state');
              setConversations(prev => prev.filter(c => c.id !== id));
              if (id === currentConversationId) {
                clearConversation();
              }
              showSuccess('Conversation removed successfully');
              return Promise.resolve();
            }
            
            if (error.response?.status === 401) {
              showWarning('Authentication failed. Please refresh and try again.');
              return Promise.reject('Authentication failed');
            }
          }
          handleApiError(error, t);
          return Promise.reject(error);
        }
      },
      onCancel: () => {
        console.log('Delete conversation cancelled');
      }
    });
  };

  const openEditTitleModal = () => {
    setNewTitle(currentConversation);
    setEditTitleModalVisible(true);
  };

  const saveEditedTitle = async () => {
    if (newTitle.trim() && currentConversationId) {
      try {
        await axios.put(`/api/v1/conversations/${currentConversationId}`, {
          title: newTitle.trim()
        }, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          }
        });
        
        setCurrentConversation(newTitle.trim());
        fetchConversations();
        setEditTitleModalVisible(false);
        showSuccess(t('common.titleUpdated'));
      } catch (error) {
        const customErrorMap = {
          404: t('errors.conversationNotFound')
        };
        handleApiError(error, t, customErrorMap);
      }
    }
  };

  const startNewConversation = () => {
    clearConversation();
    if (isMobile) {
      setSidebarVisible(false);
    }
  };

  const conversationActions = (
    <Menu>
      <Menu.Item key="save" icon={<SaveOutlined />} onClick={saveNewConversation}>
        Save conversation
      </Menu.Item>
      <Menu.Item key="rename" icon={<EditOutlined />} onClick={openEditTitleModal} disabled={!currentConversationId}>
        Rename conversation
      </Menu.Item>
      <Menu.Item key="clear" icon={<DeleteOutlined />} onClick={clearConversation}>
        Clear conversation
      </Menu.Item>
      <Menu.Item key="download" icon={<DownloadOutlined />} onClick={downloadConversation}>
        Download conversation
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="llm-config" icon={<SettingOutlined />} onClick={() => navigate('/console/llm-config')}>
        {t('navbar.llmConfig')}
      </Menu.Item>
    </Menu>
  );

  const messageContextMenu = (message: Message) => (
    <Menu>
      <Menu.Item key="copy" icon={<CopyOutlined />} onClick={() => copyMessage(message.content)}>
        {t('common.copy')}
      </Menu.Item>
    </Menu>
  );

  // Custom Markdown renderers for better syntax highlighting
  const markdownRenderers = {
    code(props: any) {
      const { node, inline, className, children, ...rest } = props;
      const match = /language-(\w+)/.exec(className || '');
      return !inline && match ? (
        <SyntaxHighlighter
          style={tomorrow as any}
          language={match[1]}
          PreTag="div"
          {...rest}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      ) : (
        <code className={className} {...rest}>
          {children}
        </code>
      );
    }
  };

  // Render a message with or without attachment
  const renderMessage = (message: Message) => {
    const isUser = message.role === 'user';
    
    return (
      <div style={{ width: '100%' }}>
        {/* Message content with markdown */}
        <div 
          className="markdown-content" 
          style={{ 
            width: '100%',
            color: '#3c4043'
          }}
        >
          <ReactMarkdown 
            remarkPlugins={[remarkGfm]} 
            rehypePlugins={[rehypeRaw]}
            components={{
              ...markdownRenderers,
              // Override styles for consistent dark text
              p: ({ children, ...props }) => (
                <p {...props} style={{ 
                  margin: '0 0 8px 0', 
                  color: '#3c4043',
                  lineHeight: '1.5'
                }}>
                  {children}
                </p>
              ),
              h1: ({ children, ...props }) => (
                <h1 {...props} style={{ 
                  color: '#3c4043',
                  fontSize: '20px',
                  fontWeight: 600,
                  margin: '0 0 12px 0'
                }}>
                  {children}
                </h1>
              ),
              h2: ({ children, ...props }) => (
                <h2 {...props} style={{ 
                  color: '#3c4043',
                  fontSize: '18px',
                  fontWeight: 600,
                  margin: '0 0 10px 0'
                }}>
                  {children}
                </h2>
              ),
              h3: ({ children, ...props }) => (
                <h3 {...props} style={{ 
                  color: '#3c4043',
                  fontSize: '16px',
                  fontWeight: 600,
                  margin: '0 0 8px 0'
                }}>
                  {children}
                </h3>
              ),
              ul: ({ children, ...props }) => (
                <ul {...props} style={{ 
                  color: '#3c4043',
                  margin: '0 0 8px 0',
                  paddingLeft: '20px'
                }}>
                  {children}
                </ul>
              ),
              ol: ({ children, ...props }) => (
                <ol {...props} style={{ 
                  color: '#3c4043',
                  margin: '0 0 8px 0',
                  paddingLeft: '20px'
                }}>
                  {children}
                </ol>
              ),
              li: ({ children, ...props }) => (
                <li {...props} style={{ 
                  color: '#3c4043',
                  margin: '0 0 4px 0'
                }}>
                  {children}
                </li>
              ),
              blockquote: ({ children, ...props }) => (
                <blockquote {...props} style={{ 
                  color: '#5f6368',
                  borderLeft: '3px solid #e8eaed',
                  paddingLeft: '12px',
                  margin: '0 0 8px 0',
                  fontStyle: 'italic'
                }}>
                  {children}
                </blockquote>
              ),
              code: ({ children, className, ...props }) => {
                // Inline code
                if (!className) {
                  return (
                    <code {...props} style={{
                      backgroundColor: isUser ? 'rgba(0, 0, 0, 0.1)' : '#f1f3f4',
                      color: '#d73a49',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      fontSize: '0.9em',
                      fontFamily: 'Monaco, Consolas, "Courier New", monospace'
                    }}>
                      {children}
                    </code>
                  );
                }
                // Block code - let SyntaxHighlighter handle it
                return <code className={className} {...props}>{children}</code>;
              },
              pre: ({ children, ...props }) => (
                <pre {...props} style={{ 
                  backgroundColor: isUser ? 'rgba(0, 0, 0, 0.05)' : '#f8f9fa',
                  color: '#3c4043',
                  padding: '12px',
                  borderRadius: '8px',
                  margin: '8px 0',
                  overflow: 'auto',
                  fontSize: '14px',
                  fontFamily: 'Monaco, Consolas, "Courier New", monospace'
                }}>
                  {children}
                </pre>
              ),
              a: ({ children, href, ...props }) => (
                <a 
                  {...props} 
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ 
                    color: '#1a73e8',
                    textDecoration: 'underline'
                  }}
                >
                  {children}
                </a>
              )
            }}
          >
            {message.content}
          </ReactMarkdown>
        </div>

        {/* Attachment if present */}
        {message.hasAttachment && message.attachmentUrl && (
          <div style={{ marginTop: 12, marginBottom: 12 }}>
            {message.attachmentType === 'image' ? (
              <div style={{ 
                borderRadius: '8px',
                overflow: 'hidden',
                maxWidth: '100%'
              }}>
                <img 
                  src={message.attachmentUrl} 
                  alt={message.attachmentName || 'Attached image'} 
                  style={{ 
                    maxWidth: '100%',
                    maxHeight: '300px',
                    objectFit: 'contain'
                  }} 
                />
              </div>
            ) : (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px 12px',
                background: isUser ? 'rgba(0, 0, 0, 0.05)' : '#f5f5f5',
                borderRadius: '8px',
                marginTop: '8px'
              }}>
                <FileOutlined style={{ 
                  fontSize: 24, 
                  marginRight: 8, 
                  color: '#1890ff' 
                }} />
                <div style={{ flex: 1, overflow: 'hidden' }}>
                  <div style={{ 
                    fontWeight: 'bold', 
                    whiteSpace: 'nowrap', 
                    overflow: 'hidden', 
                    textOverflow: 'ellipsis',
                    color: '#3c4043'
                  }}>
                    {message.attachmentName}
                  </div>
                </div>
                <a 
                  href={message.attachmentUrl} 
                  download={message.attachmentName}
                  onClick={(e) => e.stopPropagation()}
                >
                  <Button 
                    type="text" 
                    size="small" 
                    icon={<DownloadOutlined style={{ color: '#1890ff' }} />} 
                  />
                </a>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Sidebar with conversation history - 参考Gemini设计
  const sidebarContent = (
        <div style={{ 
      height: '100vh',
          display: 'flex',
      flexDirection: 'column',
      backgroundColor: 'white',
      borderRight: '1px solid #e8eaed'
    }}>
      {/* Sidebar Header */}
      <div style={{
        padding: '12px 16px',
        borderBottom: '1px solid #e8eaed',
        display: 'flex',
          alignItems: 'center',
        justifyContent: 'center', // 居中对齐，移除折叠按钮后
        gap: '8px'
      }}>
        {/* Close button for mobile */}
        {isMobile && (
            <Button 
              type="text" 
            icon={<CloseOutlined />}
            onClick={() => setSidebarVisible(false)}
            style={{
              color: '#5f6368',
              padding: '4px',
              minWidth: 'auto',
              position: 'absolute',
              left: '16px'
            }}
          />
        )}
        
        <Button 
          type="primary"
          icon={<PlusOutlined />}
          onClick={startNewConversation}
          style={{
            backgroundColor: '#1a73e8',
            borderColor: '#1a73e8',
            borderRadius: '8px',
            fontWeight: 500,
            height: '36px'
          }}
        >
          {t('chat.newChat')}
        </Button>
        </div>

      {/* Conversations List */}
      <div style={{ 
        flex: 1,
        overflowY: 'auto',
        padding: '8px'
      }}>
        {fetchingConversations ? (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center',
            height: '200px'
          }}>
            <Spin />
          </div>
        ) : conversations.length === 0 ? (
          <div style={{
            textAlign: 'center',
            padding: '40px 20px',
            color: '#5f6368'
          }}>
            <Text type="secondary">{t('chat.emptyState')}</Text>
          </div>
        ) : (
    <List
            dataSource={conversations}
            renderItem={(conversation) => (
          <List.Item 
            style={{ 
                  border: 'none',
                  padding: '2px 0',
                  cursor: 'pointer'
                }}
                onClick={() => {
                  loadConversation(conversation);
                  if (isMobile) {
                    setSidebarVisible(false);
                  }
            }}
          >
            <div style={{
              width: '100%',
                  padding: '12px',
                  borderRadius: '8px',
                  backgroundColor: currentConversationId === conversation.id ? '#f1f3f4' : 'transparent',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  transition: 'background-color 0.2s'
                }}>
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div style={{
                      fontSize: '14px',
                      fontWeight: 500,
                      color: '#3c4043',
                      marginBottom: '4px',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      {conversation.title}
                    </div>
                    <div style={{
                      fontSize: '12px',
                      color: '#5f6368',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      {conversation.lastUpdated.toLocaleDateString()}
                    </div>
                  </div>
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      console.log('Delete button clicked for conversation:', conversation.id);
                      e.preventDefault();
                      e.stopPropagation();
                      deleteConversation(conversation.id, e);
                    }}
                style={{
                      color: '#5f6368',
                      marginLeft: '8px',
                      opacity: 0.7,
                      transition: 'opacity 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.opacity = '1';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.opacity = '0.7';
                    }}
                  />
            </div>
          </List.Item>
            )}
          />
        )}
      </div>
    </div>
  );

  const getMessageWidth = (content: string, hasAttachment: boolean = false) => {
    // 简化宽度计算，防止水平滚动
    return 'auto'; // 让消息宽度自适应，不超过容器的70%
  };

  // 获取用户显示名称
  const getUserDisplayName = () => {
    if (user?.name) {
      return user.name;
    } else if (user?.email) {
      // 从邮箱中提取用户名部分
      return user.email.split('@')[0];
    }
    return 'there';
  };

  // 从URL参数加载特定对话
  useEffect(() => {
    if (conversationId && conversations.length > 0) {
      const conversation = conversations.find(c => c.id === conversationId);
      if (conversation && conversation.id !== currentConversationId) {
        console.log('Loading conversation from URL:', conversation.id);
        loadConversation(conversation);
      }
    }
  }, [conversationId, conversations.length, currentConversationId]);

  // Main Chat Content - 重新设计的布局
  const chatContent = (
    <div 
      className="chat-main-layout"
      style={{ 
        height: '100vh',
        width: '100%',
        maxWidth: '100vw',
        display: 'flex', 
        flexDirection: 'column',
        backgroundColor: '#f8f9fa',
        fontFamily: '"Google Sans", Roboto, Arial, sans-serif',
        position: 'fixed',
        top: 0,
        left: sidebarCollapsed || isMobile ? 0 : 280,
        right: 0,
        overflow: 'hidden',
        transition: 'left 0.2s',
        zIndex: 1000 // 确保在侧边栏下方
      }}>
      {/* Header - 简化版，移除折叠按钮和模型选择 */}
      <div 
        style={{ 
          padding: '12px 24px',
          backgroundColor: '#f8f9fa',
          borderBottom: '1px solid #e0e0e0',
        display: 'flex', 
          alignItems: 'center',
          justifyContent: 'flex-end', // 只保留右侧的操作按钮
          boxShadow: '0 1px 4px rgba(0,0,0,0.08)',
          flexShrink: 0,
          height: '48px',
          zIndex: 100
        }}>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Dropdown 
            overlay={conversationActions} 
            trigger={['click']}
            getPopupContainer={() => document.body}
          >
        <Button
          type="text"
              icon={<EllipsisOutlined style={{ color: '#5f6368' }} />}
            style={{ 
                height: '32px',
                width: '32px',
                padding: '8px',
                borderRadius: '8px',
                backgroundColor: '#f1f3f4',
                border: '1px solid #e8eaed'
              }}
            />
          </Dropdown>
        </div>
      </div>

      {/* 聊天控制栏 - 新增，包含折叠按钮和模型选择 */}
          <div style={{ 
        padding: '8px 16px',
        backgroundColor: '#f8f9fa', // 改为浅灰色，与聊天区域一致
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        flexShrink: 0,
        height: '56px',
        marginTop: '4px' // 增加与导航栏的间隔
      }}>

        {/* 侧边栏折叠按钮 */}
        <Button
          type="text"
          icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={() => {
            console.log('Sidebar toggle clicked, current state:', sidebarCollapsed);
            if (isMobile) {
              setSidebarVisible(!sidebarVisible);
              console.log('Mobile: Setting sidebar visible to:', !sidebarVisible);
            } else {
              setSidebarCollapsed(!sidebarCollapsed);
              console.log('Desktop: Setting sidebar collapsed to:', !sidebarCollapsed);
            }
          }}
          style={{ 
            color: '#5f6368',
            padding: '6px',
            backgroundColor: 'transparent',
            border: 'none',
            borderRadius: '8px',
            height: '32px',
            width: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px',
            transition: 'all 0.2s ease'
          }}
        />

        {/* LLM 提供商选择 */}
        <div style={{ marginTop: '4px' }}> {/* 添加容器并往下移动 */}
        <Dropdown
          overlay={
            <Menu style={{ backgroundColor: 'white', border: 'none', borderRadius: '12px', minWidth: '280px', boxShadow: '0 4px 20px rgba(0,0,0,0.15)' }}>
              {providers.length === 0 ? (
                <Menu.Item disabled>
                  <div style={{ color: '#9aa0a6', padding: '12px', fontSize: '13px' }}>
                    {fetchingProviders ? 'Loading providers...' : 'No providers available'}
                  </div>
                </Menu.Item>
              ) : (
                providers.map((provider) => (
                  <Menu.Item 
                    key={provider.id}
                    onClick={() => {
                      console.log('Selected provider:', provider.id, provider.alias);
                      setSelectedProvider(provider.id);
                    }}
                    style={{
                      backgroundColor: selectedProvider === provider.id ? '#f8f9fa' : 'transparent',
                      color: '#3c4043',
                      padding: '10px 16px',
                      margin: '4px 8px',
                      borderRadius: '8px',
                      border: 'none'
                    }}
                  >
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <span style={{ 
                          fontWeight: selectedProvider === provider.id ? 500 : 400,
                          fontSize: '13px',
                          color: '#3c4043'
                        }}>
                          {provider.alias}
                    </span>
                        {provider.isDefault && (
                          <Tag color="#e8f5e8" style={{ color: '#1a73e8', fontSize: '11px', border: 'none' }}>{t('llmConfig.defaultLabel')}</Tag>
                        )}
                        {provider.isBuiltIn && (
                          <Tag color="#f0f9ff" style={{ color: '#0ea5e9', fontSize: '11px', border: 'none' }}>System</Tag>
                        )}
                  </div>
                      {selectedProvider === provider.id && (
                        <span style={{ color: '#1a73e8', fontSize: '14px' }}>✓</span>
                      )}
          </div>
                  </Menu.Item>
                ))
              )}
              <Menu.Divider style={{ margin: '8px 0', backgroundColor: '#f1f3f4' }} />
              <Menu.Item 
                key="manage"
                icon={<SettingOutlined style={{ fontSize: '14px' }} />}
                onClick={() => navigate('/console/llm-config')}
                style={{ color: '#5f6368', padding: '10px 16px', margin: '4px 8px', fontSize: '13px' }}
              >
                {t('llmConfig.manageProviders')}
              </Menu.Item>
            </Menu>
          }
          trigger={['click']}
          disabled={providers.length === 0}
          getPopupContainer={() => document.body}
          placement="bottomLeft"
        >
          <Button
            style={{ 
              backgroundColor: '#e8eaed', // 改为灰色背景
              border: 'none',
              borderRadius: '20px',
              color: '#5f6368',
              fontWeight: 400,
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              fontSize: '13px',
              padding: '0 12px',
              minWidth: 'auto',
              width: 'auto',
              boxShadow: 'none',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#dadce0'; // hover时的深灰色
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#e8eaed'; // 恢复灰色
            }}
          >
            <span style={{ flex: 'none', textAlign: 'left', color: '#3c4043', whiteSpace: 'nowrap' }}>
              {providers.find(p => p.id === selectedProvider)?.alias || 
               (fetchingProviders ? 'Loading...' : 'Select Model')}
            </span>
            <DownOutlined style={{ fontSize: '10px', color: '#9aa0a6', flexShrink: 0 }} />
          </Button>
        </Dropdown>
        </div> {/* 结束容器 */}

        {/* 当前对话标题 - 移除显示 */}
        <div style={{ flex: 1 }} />
    </div>

      {/* Messages Area - 居中布局，调整padding考虑新的控制栏 */}
    <div style={{ 
        flex: 1,
        overflowY: 'auto',
        overflowX: 'hidden',
        backgroundColor: '#f8f9fa',
      display: 'flex', 
        justifyContent: 'center',
        paddingBottom: '140px',
        paddingTop: '24px' // 增加padding考虑新的控制栏
      }}>
        {messages.length === 0 ? (
          <div style={{ 
            width: '100%',
      height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '40px 20px',
            textAlign: 'center'
          }}>
            <div style={{ maxWidth: '600px' }}>
              <Title 
                level={1} 
                className="gradient-text"
                style={{ 
                  fontWeight: 600, 
                  marginBottom: '16px',
                  fontSize: isMobile ? '2.5rem' : '3.5rem',
                  lineHeight: '1.2'
                }}
              >
                Hello, {getUserDisplayName()}
              </Title>
              <Text 
                className="gradient-subtitle"
                style={{ 
                  fontSize: '18px',
                  fontWeight: 500,
                  display: 'block'
                }}
              >
                {providers.length === 0 
                  ? t('llmConfig.selectProvider')
                  : 'How can I help you today?'
                }
              </Text>
            </div>
          </div>
        ) : (
          <div style={{ 
      width: '100%',
            maxWidth: '800px',
            padding: '0 16px'
          }}>
            {messages.map((message, index) => (
              <div key={message.id} style={{ 
                marginBottom: '24px',
                display: 'flex',
                justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                width: '100%'
              }}>
      <div style={{
        display: 'flex',
                  gap: '12px',
                  alignItems: 'flex-start',
                  maxWidth: isMobile ? '85%' : '75%',
                  flexDirection: message.role === 'user' ? 'row-reverse' : 'row',
                  justifyContent: 'flex-start'
                }}>
                  <Avatar 
                    size={36}
                    style={{
                      backgroundColor: message.role === 'user' ? '#1a73e8' : '#34a853',
                      flexShrink: 0
                    }}
                    icon={message.role === 'user' ? <UserOutlined /> : <RobotOutlined />}
                  />
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div style={{
                      backgroundColor: message.role === 'user' ? '#f5f5f5' : 'white',
                      color: '#3c4043',
                      borderRadius: message.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',
                      padding: '12px 16px',
                      border: message.role === 'user' ? '1px solid #e0e0e0' : '1px solid #e8eaed',
                      fontSize: '14px',
                      lineHeight: '1.5',
                      wordBreak: 'break-word',
                      boxShadow: message.role === 'user' ? '0 1px 2px rgba(0,0,0,0.1)' : '0 1px 2px rgba(0,0,0,0.1)',
                      position: 'relative',
                      overflowWrap: 'break-word',
                      wordWrap: 'break-word'
                    }}
                    onMouseEnter={(e) => {
                      const timeDiv = e.currentTarget.nextElementSibling as HTMLElement;
                      if (timeDiv && timeDiv.classList.contains('message-time')) {
                        timeDiv.style.opacity = '1';
                        timeDiv.style.visibility = 'visible';
                      }
                    }}
                    onMouseLeave={(e) => {
                      const timeDiv = e.currentTarget.nextElementSibling as HTMLElement;
                      if (timeDiv && timeDiv.classList.contains('message-time')) {
                        timeDiv.style.opacity = '0';
                        timeDiv.style.visibility = 'hidden';
                      }
                    }}
                    >
                      {renderMessage(message)}
                    </div>
                    
                    {/* Message timestamp and actions - Hidden by default, shown on hover */}
                    <div 
                      className="message-time"
                      style={{ 
                        display: 'flex', 
                        gap: '8px',
                        marginTop: '8px',
                        justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                        alignItems: 'center',
                        opacity: 0,
                        visibility: 'hidden',
                        transition: 'opacity 0.2s, visibility 0.2s'
                      }}
                    >
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {message.timestamp.toLocaleString()}
                      </Text>
            <Button 
              type="text" 
                        size="small" 
                        icon={<CopyOutlined />}
                        onClick={() => copyMessage(message.content)}
                        style={{ 
                          color: '#5f6368',
                          opacity: 0.6,
                          padding: '4px'
                        }}
                      />
        </div>
      </div>
                </div>
              </div>
            ))}

            {loading && (
      <div style={{ 
        display: 'flex', 
                justifyContent: 'flex-start',
                marginBottom: '24px',
        width: '100%'
      }}>
          <div style={{ 
                  display: 'flex',
                  gap: '12px',
                  alignItems: 'flex-start',
                  maxWidth: isMobile ? '85%' : '75%'
                }}>
                  <Avatar 
                    size={36}
                    style={{ backgroundColor: '#34a853' }}
                    icon={<RobotOutlined />}
                  />
                  <div style={{
                    backgroundColor: 'white',
                    borderRadius: '18px 18px 18px 4px',
                    padding: '12px 16px',
                    border: '1px solid #e8eaed',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }}>
                    <Spin size="small" />
                    <Text style={{ color: '#5f6368' }}>{t('chat.responseLoading')}</Text>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} style={{ height: '20px' }} />
          </div>
        )}
      </div>

      {/* Fixed Input Area */}
      <div style={{ 
        position: 'fixed',
        bottom: 0,
        left: sidebarCollapsed || isMobile ? 0 : 280,
        right: 0,
        backgroundColor: 'white',
        padding: isMobile ? '16px' : '24px',
        borderTop: '1px solid #e8eaed',
        zIndex: 1001,
        transition: 'left 0.2s',
        display: 'flex',
        justifyContent: 'center'
      }}>
        <div style={{
          maxWidth: '800px',
          width: '100%',
          position: 'relative'
        }}>
          <div style={{
            backgroundColor: 'white',
            border: '1px solid #dadce0',
            borderRadius: '24px',
            padding: isMobile ? '12px 16px 8px' : '16px 20px 12px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            display: 'flex', 
            flexDirection: 'column', 
            gap: isMobile ? '8px' : '12px'
          }}>
            {/* Main input row */}
            <div style={{
              display: 'flex',
              alignItems: 'flex-end',
              gap: '8px',
              minHeight: '32px'
            }}>
              {/* Status Indicator */}
              <div style={{
                display: 'flex',
            alignItems: 'center', 
            justifyContent: 'center',
                width: '32px',
                height: '32px',
                flexShrink: 0
              }}>
                {liveStatus === 'ready' && (
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor: '#34a853'
                  }} />
                )}
                {liveStatus === 'thinking' && (
                  <Spin size="small" style={{ color: '#f39c12' }} />
                )}
                {liveStatus === 'running' && (
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor: '#1a73e8',
                    animation: 'pulse 1.5s infinite'
                  }} />
                )}
                {liveStatus === 'failed' && (
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor: '#ea4335'
                  }} />
                )}
              </div>

              {/* Plus Icon */}
              <Button
                type="text"
                size="small"
                icon={<PlusOutlined style={{ color: '#5f6368', fontSize: '16px' }} />}
                onClick={() => setUploadModalVisible(true)}
                disabled={isStreaming}
                style={{
                  border: 'none',
                  padding: '6px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                  height: '32px',
                  width: '32px',
                  opacity: isStreaming ? 0.5 : 1
                }}
              />

              {/* Text Input Area */}
              <div style={{ flex: 1, minHeight: '32px' }}>
                <TextArea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      if (!isStreaming) {
                        handleSend();
                      }
                    }
                  }}
                  placeholder={isStreaming ? t('chat.aiResponding') : `${t('chat.askSomething')} (Shift+Enter换行)`}
                  autoSize={{ minRows: 1, maxRows: 4 }}
                  className="left-aligned-textarea"
                  disabled={isStreaming}
                  style={{
                    border: 'none',
                    backgroundColor: 'transparent',
                    fontSize: '16px',
                    color: '#3c4043',
                    padding: '6px 12px',
                    resize: 'none',
                    boxShadow: 'none',
                    textAlign: 'left',
                    width: '100%',
                    lineHeight: '1.5',
                    opacity: isStreaming ? 0.5 : 1
                  }}
                />
          </div>

              {/* Send/Interrupt Button */}
              <Button
                type="primary"
                icon={isStreaming ? <CloseOutlined /> : <SendOutlined />}
                onClick={isStreaming ? interruptStreaming : handleSend}
                disabled={!isStreaming && (!input.trim() || loading || providers.length === 0)}
                style={{
                  backgroundColor: isStreaming ? '#ea4335' : (input.trim() ? '#1a73e8' : '#f0f0f0'),
                  borderColor: isStreaming ? '#ea4335' : (input.trim() ? '#1a73e8' : '#f0f0f0'),
                  color: isStreaming ? 'white' : (input.trim() ? 'white' : '#999'),
                  borderRadius: '50%',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                  padding: 0
                }}
              />
            </div>

            {/* Bottom row with feature buttons - only show on desktop */}
            {!isMobile && (
          <div style={{ 
            display: 'flex',
                alignItems: 'center', 
                justifyContent: 'center',
                gap: '12px',
                paddingTop: '4px',
                borderTop: '1px solid #f0f0f0'
              }}>
                {/* Deep Research Button */}
                <Button
                  size="small"
              style={{
                    backgroundColor: 'transparent',
                    border: 'none',
                    color: '#5f6368',
                    fontSize: '12px',
                    height: '28px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    padding: '0 8px',
                    borderRadius: '14px'
                  }}
                >
                  <SearchOutlined style={{ fontSize: '14px' }} />
                  Deep Research
                </Button>

                {/* Canvas Button */}
                <Button
                  size="small"
                  style={{
                    backgroundColor: 'transparent',
                    border: 'none',
                    color: '#5f6368',
                    fontSize: '12px',
                    height: '28px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    padding: '0 8px',
                    borderRadius: '14px'
                  }}
                >
                  <FileTextOutlined style={{ fontSize: '14px' }} />
                  Canvas
                </Button>

                {/* Voice Input */}
                <Button
                  type="text"
                  size="small"
                  icon={<AudioOutlined style={{ color: '#5f6368', fontSize: '14px' }} />}
                  style={{
                    border: 'none',
                    padding: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '50%',
                    width: '28px',
                    height: '28px',
                    backgroundColor: 'transparent'
                  }}
                />
          </div>
        )}
          </div>
        </div>
      </div>
      
      {/* Upload Modal */}
      <Modal
        title={t('chat.uploadFile')}
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false);
          setFileList([]);
        }}
        footer={[
          <Button 
            key="cancel" 
            onClick={() => {
              setUploadModalVisible(false);
              setFileList([]);
            }}
          >
            {t('common.cancel')}
          </Button>,
          <Button 
            key="upload" 
            type="primary" 
            onClick={handleUpload}
            loading={uploadLoading}
            disabled={fileList.length === 0}
          >
            {t('chat.send')}
          </Button>
        ]}
      >
        <Dragger 
          name="file"
          multiple={false}
          fileList={fileList}
          beforeUpload={(file) => {
            setFileList([file]);
            return false;
          }}
          onRemove={() => {
            setFileList([]);
          }}
        >
          <p className="ant-upload-drag-icon">
            <FileImageOutlined style={{ fontSize: 48, color: '#1890ff' }} />
          </p>
          <p className="ant-upload-text">{t('chat.clickOrDragFile')}</p>
          <p className="ant-upload-hint">
            {t('chat.supportedFileTypes')}
          </p>
        </Dragger>
        <div style={{ marginTop: 16 }}>
          <Input
            placeholder={t('chat.addMessageWithFile')}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            style={{ width: '100%' }}
          />
        </div>
      </Modal>

      {/* Edit Title Modal */}
        <Modal
          title={t('common.edit')}
          open={editTitleModalVisible}
          onOk={saveEditedTitle}
          onCancel={() => setEditTitleModalVisible(false)}
        >
          <Input 
            value={newTitle} 
            onChange={(e) => setNewTitle(e.target.value)} 
          placeholder={t('chat.conversationTitle')}
          />
        </Modal>
      </div>
    );

  return (
    <Layout style={{ height: '100vh' }}>
      {/* Styles for centered placeholder text */}
      <style>
        {`
          .left-aligned-textarea .ant-input {
            text-align: left !important;
          }
          .left-aligned-textarea .ant-input::placeholder {
            text-align: left !important;
            color: #9aa0a6 !important;
          }
          .left-aligned-textarea .ant-input:focus {
            text-align: left !important;
          }
          
          .gradient-text {
            color: #667eea;
            animation: colorShift 8s ease-in-out infinite;
          }
          
          .gradient-subtitle {
            color: #4facfe;
          }
          
          @keyframes colorShift {
            0%, 100% {
              color: #667eea;
            }
            25% {
              color: #f093fb;
            }
            50% {
              color: #4facfe;
            }
            75% {
              color: #43e97b;
            }
          }
          
          @keyframes pulse {
            0% {
              box-shadow: 0 0 0 0 rgba(26, 115, 232, 0.7);
            }
            70% {
              box-shadow: 0 0 0 10px rgba(26, 115, 232, 0);
            }
            100% {
              box-shadow: 0 0 0 0 rgba(26, 115, 232, 0);
            }
          }
          
          /* 确保下拉菜单在最顶层 */
          .ant-dropdown {
            z-index: 9999 !important;
          }
        `}
      </style>
      
      {/* Desktop Sidebar - 修改为在导航栏下面 */}
      {!isMobile && (
      <Sider
          width={280}
        collapsed={sidebarCollapsed}
          collapsedWidth={0}
        trigger={null}
        style={{ 
            background: 'white',
            borderRight: '1px solid #e8eaed',
            transition: 'all 0.2s',
            position: 'fixed',
            left: 0,
            top: 64, // 改回64px，在导航栏下面
            bottom: 0,
            zIndex: 1010, // 提高z-index确保在聊天内容之上
            boxShadow: sidebarCollapsed ? 'none' : '2px 0 8px rgba(0,0,0,0.1)' // 添加阴影增强可见性
          }}
        >
          {sidebarContent}
        </Sider>
      )}

      {/* Mobile Drawer */}
      {isMobile && (
        <Drawer
          title={null}
          placement="left"
          width={280}
          onClose={() => setSidebarVisible(false)}
          open={sidebarVisible}
          bodyStyle={{ padding: 0 }}
          headerStyle={{ display: 'none' }}
          style={{ zIndex: 1020 }}
          getContainer={false}
        >
          {sidebarContent}
        </Drawer>
      )}

      {/* Main Content */}
      <Layout>
        <Content style={{ 
          margin: 0,
          marginLeft: sidebarCollapsed || isMobile ? 0 : 280,
          marginTop: 0,
          transition: 'all 0.2s',
          position: 'relative'
        }}>
        {chatContent}
      </Content>
      </Layout>
    </Layout>
  );
};

export default Chat; 