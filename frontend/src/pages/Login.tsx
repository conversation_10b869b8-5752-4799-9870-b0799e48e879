import { useEffect, useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { Button, Typography, Card, Spin } from 'antd';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

const Login = () => {
  const { user, loading, login } = useAuth();
  const navigate = useNavigate();
  const [redirecting, setRedirecting] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    console.log("Login component - auth state:", { user, loading });

    if (user) {
      console.log("User already logged in, redirecting to console");
      navigate('/console');
    } else if (!loading && !redirecting) {
      // 只有当不在加载状态且未开始重定向时才触发登录
      // 这样可以防止多次触发登录过程
      console.log("User not logged in and not loading, showing login page");
    }
  }, [user, loading, navigate, redirecting]);

  const handleLogin = () => {
    console.log("Login button clicked");
    setRedirecting(true);
    login();
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', marginTop: '20vh' }}>
        <Spin size="large" />
        <div style={{ marginTop: '20px' }}>
          <Text>{t('common.loading')}</Text>
        </div>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: '600px', margin: '100px auto' }}>
      <Card>
        <div style={{ textAlign: 'center' }}>
          <Title level={2}>{t('login.welcomeBack')}</Title>
          <Text style={{ display: 'block', marginBottom: '24px' }}>
            {t('login.title')}
          </Text>
          <Button 
            type="primary" 
            size="large" 
            onClick={handleLogin}
            loading={redirecting}
          >
            {redirecting ? t('common.loading') : t('login.loginButton')}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default Login; 