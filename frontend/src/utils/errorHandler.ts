import { message } from 'antd';
import axios, { AxiosError } from 'axios';
import { TFunction } from 'i18next';

/**
 * Standard error message style to maintain consistency
 */
const errorStyle = { marginTop: '20vh' };

/**
 * Handles API errors with consistent messaging
 */
export const handleApiError = (
  error: unknown, 
  t: TFunction,
  customErrorMap?: Record<number, string>
) => {
  console.error('API Error:', error);

  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;
    
    if (axiosError.response) {
      const status = axiosError.response.status;
      let errorMsg = '';
      
      // Check if there's a custom error message for this status code
      if (customErrorMap && customErrorMap[status]) {
        errorMsg = customErrorMap[status];
      } else {
        // Use default error messages
        switch (status) {
          case 400:
            errorMsg = t('errors.badRequest');
            break;
          case 401:
            errorMsg = t('errors.unauthorized');
            break;
          case 402:
            errorMsg = t('errors.paymentRequired');
            break;
          case 403:
            errorMsg = t('errors.forbidden');
            break;
          case 404:
            errorMsg = t('errors.notFound');
            break;
          case 409:
            errorMsg = t('errors.conflict');
            break;
          case 429:
            errorMsg = t('errors.tooManyRequests');
            break;
          case 500:
            errorMsg = t('errors.serverError');
            break;
          default:
            errorMsg = `${t('errors.generic')} (${status})`;
        }
      }
      
      // Extract server error message if available
      const serverMsg = axiosError.response.data && 
        typeof axiosError.response.data === 'object' && 
        'error' in axiosError.response.data ? 
        (axiosError.response.data as { error: string }).error : 
        undefined;
      
      // Display the error message
      message.error({
        content: serverMsg ? `${errorMsg}: ${serverMsg}` : errorMsg,
        duration: 5,
        style: errorStyle
      });
    } else if (axiosError.request) {
      // The request was made but no response was received
      message.error({
        content: t('errors.network'),
        duration: 5,
        style: errorStyle
      });
    } else {
      // Something happened in setting up the request
      message.error({
        content: t('errors.request'),
        duration: 5,
        style: errorStyle
      });
    }
  } else {
    // Handle non-Axios errors
    message.error({
      content: t('errors.unknown'),
      duration: 5,
      style: errorStyle
    });
  }
};

/**
 * Displays a success message with consistent styling
 */
export const showSuccess = (content: string, duration = 3) => {
  message.success({
    content,
    duration,
    style: errorStyle
  });
};

/**
 * Displays a warning message with consistent styling
 */
export const showWarning = (content: string, duration = 4) => {
  message.warning({
    content,
    duration,
    style: errorStyle
  });
};

/**
 * Displays an info message with consistent styling
 */
export const showInfo = (content: string, duration = 3) => {
  message.info({
    content,
    duration,
    style: errorStyle
  });
}; 