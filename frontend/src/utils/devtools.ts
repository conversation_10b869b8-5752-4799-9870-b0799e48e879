/**
 * 开发工具相关的工具函数
 */

// 检测当前是否为开发模式
export const checkDevelopmentMode = (): boolean => {
  // 1. 首先检查 Vite 开发模式 (最可靠的方式)
  if (import.meta.env.DEV) {
    return true;
  }

  // 2. 检查是否在本地环境
  const isLocalhost = window.location.hostname === 'localhost' || 
                     window.location.hostname === '127.0.0.1' ||
                     window.location.hostname.includes('local');
                     
  // 3. 检查常见的开发端口
  const isDevPort = window.location.port === '5173' || // Vite dev server
                   window.location.port === '3000' ||  // React dev server
                   window.location.port === '8080';    // Backend dev server

  // 4. 检查URL参数中是否包含开发标识
  const hasDevParam = window.location.search.includes('dev=true') ||
                     window.location.search.includes('debug=true');

  // 5. 检查localStorage中的开发模式标识
  const hasDevFlag = localStorage.getItem('dev-mode') === 'true';

  return isLocalhost || isDevPort || hasDevParam || hasDevFlag;
};

// 检测后端开发模式（可选的后端API检测，作为补充）
export const checkBackendDevelopmentMode = async (): Promise<boolean | null> => {
  try {
    const response = await fetch('/api/v1/env', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      // 设置超时时间，避免长时间等待
      signal: AbortSignal.timeout(2000)
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.isDevelopment === true;
    }
  } catch (error) {
    // 网络错误或超时，返回null表示无法检测
    console.debug('Backend development mode check failed:', error);
  }
  
  return null;
};

// 综合检测开发模式
export const isDevelopmentMode = async (): Promise<boolean> => {
  // 前端检测（立即返回结果）
  const frontendDev = checkDevelopmentMode();
  
  // 如果前端已经检测到开发模式，直接返回
  if (frontendDev) {
    return true;
  }
  
  // 尝试后端检测（异步，有超时保护）
  const backendDev = await checkBackendDevelopmentMode();
  
  // 如果后端检测成功且为开发模式，返回true
  if (backendDev === true) {
    return true;
  }
  
  // 否则返回前端检测结果
  return frontendDev;
};

// 等待DOM完全加载
const waitForDOMReady = (): Promise<void> => {
  return new Promise((resolve) => {
    if (document.readyState === 'complete') {
      resolve();
    } else {
      window.addEventListener('load', () => resolve());
    }
  });
};

// 等待一段时间确保页面布局稳定
const waitForLayoutStable = (ms: number = 500): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 动态加载 stagewise/toolbar
export const loadStageWiseToolbar = async (): Promise<void> => {
  const isDev = await isDevelopmentMode();
  
  if (isDev) {
    try {
      // 等待DOM完全加载
      await waitForDOMReady();
      
      // 额外等待确保布局稳定
      await waitForLayoutStable(300);
      
      console.log('🛠️ Starting StagWise Toolbar initialization...');
      
      // 动态导入 stagewise/toolbar (使用any来避免TypeScript模块解析问题)
      const module = await import('@stagewise/toolbar' as any) as any;
      
      if (module.initToolbar && typeof module.initToolbar === 'function') {
        // 确保body元素存在且有合适的样式
        const body = document.body;
        if (body) {
          // 确保body有最小高度，避免容器高度为0
          if (!body.style.minHeight) {
            body.style.minHeight = '100vh';
          }
        }
        
        // 使用正确的 initToolbar 函数
        module.initToolbar({
          plugins: [] // 初始化时不需要插件，可以后续添加
        });
        
        console.log('🛠️ StagWise Toolbar initialized successfully');
        
        // 验证工具栏是否正确加载
        setTimeout(() => {
          const toolbarElements = document.querySelectorAll('[data-stagewise], [class*="stagewise"], [id*="stagewise"]');
          if (toolbarElements.length > 0) {
            console.log('✅ StagWise Toolbar UI elements detected:', toolbarElements.length);
          } else {
            console.log('⚠️ StagWise Toolbar initialized but no UI elements detected yet');
          }
        }, 1000);
        
      } else {
        console.warn('⚠️ StagWise Toolbar module loaded but initToolbar function not found', {
          availableExports: Object.keys(module)
        });
      }
    } catch (error) {
      console.warn('⚠️ Failed to load StagWise Toolbar:', error);
    }
  } else {
    console.log('🚀 Running in production mode, StagWise Toolbar not loaded');
  }
};

// 初始化开发工具
export const initializeDevTools = async (): Promise<void> => {
  // 只在浏览器环境中运行
  if (typeof window !== 'undefined') {
    await loadStageWiseToolbar();
  }
};

// 手动启用开发模式（通过localStorage）
export const enableDevMode = (): void => {
  localStorage.setItem('dev-mode', 'true');
  console.log('🔧 Development mode enabled manually');
  // 重新初始化开发工具
  initializeDevTools();
};

// 手动禁用开发模式
export const disableDevMode = (): void => {
  localStorage.removeItem('dev-mode');
  console.log('🔒 Development mode disabled manually');
};

// 强制初始化工具栏，包含更多容错处理
export const forceInitStageWiseToolbar = async (): Promise<void> => {
  try {
    console.log('🔧 Force initializing StagWise Toolbar...');
    
    // 动态导入模块
    const module = await import('@stagewise/toolbar' as any) as any;
    
    if (module.initToolbar && typeof module.initToolbar === 'function') {
      // 确保有一个合适的容器
      const ensureContainer = () => {
        const body = document.body;
        const html = document.documentElement;
        
        // 设置基础样式
        if (body) {
          body.style.minHeight = '100vh';
          body.style.width = '100%';
          body.style.position = 'relative';
        }
        
        if (html) {
          html.style.minHeight = '100vh';
          html.style.width = '100%';
        }
        
        // 创建一个专用的容器给 StagWise Toolbar
        let container = document.getElementById('stagewise-container');
        if (!container) {
          container = document.createElement('div');
          container.id = 'stagewise-container';
          container.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            pointer-events: none;
            z-index: 9999;
          `;
          document.body.appendChild(container);
        }
        
        return container;
      };
      
      // 确保容器存在
      const container = ensureContainer();
      
      // 初始化工具栏
      module.initToolbar({
        plugins: []
      });
      
      console.log('✅ StagWise Toolbar force initialized successfully');
      
      // 验证初始化结果
      setTimeout(() => {
        const toolbarElements = document.querySelectorAll('[data-stagewise], [class*="stagewise"], [id*="stagewise"]');
        console.log(`🔍 Found ${toolbarElements.length} StagWise elements after force init`);
        
        if (toolbarElements.length === 0) {
          console.warn('⚠️ No StagWise elements found. The toolbar might be hidden or not rendered yet.');
          console.log('💡 You can try: devTools.forceInit() in console');
        }
      }, 2000);
      
    } else {
      console.error('❌ initToolbar function not found in module');
    }
  } catch (error) {
    console.error('❌ Force initialization failed:', error);
  }
}; 