import React, { useState, useEffect, createContext, useContext } from 'react';
import axios from 'axios';

// 定义用户类型
interface User {
  id: string;
  name?: string;
  email?: string;
  [key: string]: any;
}

// 认证上下文类型
interface AuthContextType {
  user: User | null;
  token: string | null;
  loading: boolean;
  login: () => void;
  logout: () => void;
}

// 设置axios默认baseURL
axios.defaults.baseURL = import.meta.env.VITE_API_URL || '';

// 创建认证上下文
const AuthContext = createContext<AuthContextType>({
  user: null,
  token: null,
  loading: true,
  login: () => {},
  logout: () => {},
});

// 认证提供者组件
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // 在组件挂载时检查是否已经登录
  useEffect(() => {
    console.log("AuthProvider mounted, checking login status");
    
    // 检查URL中是否有token参数（从回调重定向而来）
    const params = new URLSearchParams(window.location.search);
    const tokenFromUrl = params.get('token');
    
    if (tokenFromUrl) {
      console.log("Found token in URL, saving to localStorage");
      localStorage.setItem('auth_token', tokenFromUrl);

      // 清除 URL 中的令牌参数
      window.history.replaceState({}, document.title, window.location.pathname);
      setToken(tokenFromUrl);
      fetchUserWithToken(tokenFromUrl);
    } else {
      // 检查 localStorage 中是否有保存的令牌
      const savedToken = localStorage.getItem('auth_token');
      if (savedToken) {
        console.log("Found saved token, checking validity");
        setToken(savedToken);
        fetchUserWithToken(savedToken);
      } else {
        console.log("No token found, user not logged in");
        setLoading(false);
      }
    }
  }, []);

  // 使用令牌获取用户信息
  const fetchUserWithToken = async (authToken: string) => {
    try {
      // 设置默认请求头
      axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
      
      console.log("Fetching user info with token");
      const response = await axios.get('/api/v1/me');
      console.log("User info received:", response.data);
      
      setUser(response.data);
    } catch (error) {
      console.error("Failed to fetch user info:", error);
      // 令牌无效，清除存储
      localStorage.removeItem('auth_token');
      setToken(null);
    } finally {
      setLoading(false);
    }
  };

  // 登录 - 调用后端登录端点
  const login = () => {
    console.log("Redirecting to backend login endpoint");
    // 直接重定向到后端登录端点，让后端处理 Logto 认证
    window.location.href = '/api/v1/login';
  };

  // 登出 - 调用后端登出端点并清除本地存储
  const logout = async () => {
    console.log("Logging out");

    // 清除本地状态
    localStorage.removeItem('auth_token');
    setToken(null);
    setUser(null);

    // 清除请求头
    delete axios.defaults.headers.common['Authorization'];

    console.log("Redirecting to backend logout endpoint");
    // 重定向到后端登出端点，让后端处理 Logto 登出
    window.location.href = '/api/v1/logout';
  };

  // 提供认证上下文
  return (
    <AuthContext.Provider value={{
      user,
      token,
      loading,
      login,
      logout
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// 导出认证钩子
export const useAuth = () => useContext(AuthContext); 