:root {
  --primary-color: #1890ff;
  --background-color: #f5f5f5;
  --text-color: #000000e0;
  --secondary-color: #fafafa;
  --border-radius: 6px;
  --content-width: 100%;
}

html, body, #root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-color);
  width: 100vw;
  overflow-x: hidden;
}

/* 响应式设置 */
@media (max-width: 576px) {
  html {
    font-size: 14px;
  }
}

@media (min-width: 577px) and (max-width: 991px) {
  html {
    font-size: 15px;
  }
}

@media (min-width: 992px) {
  html {
    font-size: 16px;
  }
}

/* 美化滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保ant design组件继承高度 */
.ant-layout, .ant-layout-content {
  height: 100%;
  width: 100%;
}

/* 修复宽度限制 */
.ant-layout {
  max-width: 100vw !important;
  width: 100vw !important;
}

/* 优化 Markdown 内容显示 */
.markdown-content {
  width: 100%;
  overflow-x: auto;
}

.markdown-content pre {
  background-color: #f0f0f0;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 0.9rem;
  margin: 1rem 0;
}

.markdown-content code {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.9em;
}

.markdown-content pre code {
  padding: 0;
  background: transparent;
}

.markdown-content p {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.markdown-content h1, 
.markdown-content h2, 
.markdown-content h3, 
.markdown-content h4, 
.markdown-content h5, 
.markdown-content h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

.markdown-content ul, 
.markdown-content ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.markdown-content li {
  margin: 0.25rem 0;
}

.markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.markdown-content blockquote {
  border-left: 4px solid var(--primary-color);
  padding-left: 1rem;
  margin: 1rem 0;
  color: rgba(0, 0, 0, 0.65);
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #e8e8e8;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content th {
  background-color: #fafafa;
}

/* 响应式菜单样式优化 */
.ant-menu-overflow-item {
  display: flex !important;
  align-items: center !important;
}

@media (max-width: 767px) {
  .ant-layout-header {
    padding: 0 16px !important;
  }
}

/* 按钮悬浮效果优化 */
.ant-btn {
  transition: all 0.3s ease !important;
}

.ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.2);
}

/* 卡片样式优化 */
.ant-card {
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 确保根元素和body有合适的尺寸，避免StagWise Toolbar容器尺寸为0的问题 */
html, body {
  min-height: 100vh;
  width: 100%;
}

#root {
  min-height: 100vh;
  width: 100%;
  position: relative;
}

/* StagWise Toolbar 容器样式优化 */
[data-stagewise] {
  position: fixed !important;
  z-index: 9999 !important;
}

/* 确保StagWise工具栏不会被其他元素遮挡 */
[class*="stagewise"] {
  z-index: 9999 !important;
} 