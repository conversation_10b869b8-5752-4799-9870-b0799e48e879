:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 添加对现代Forced Colors Mode的支持 */
@media (forced-colors: active) {
  .ant-form-item-control {
    display: flex;
  }
  
  /* 确保按钮在高对比度模式下有足够的边框可见性 */
  button, 
  .ant-btn {
    forced-color-adjust: none;
    border: 1px solid ButtonText;
  }
  
  /* 确保链接在高对比度模式下可见 */
  a {
    color: LinkText;
  }
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vanilla:hover {
  filter: drop-shadow(0 0 2em #3178c6aa);
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* 添加 Markdown 内容的样式 */
.markdown-content {
  width: 100%;
  line-height: 1.6;
}

.markdown-content pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
}

.markdown-content code {
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.markdown-content p {
  margin-bottom: 1em;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5em;
  margin-bottom: 1em;
}

.markdown-content blockquote {
  border-left: 4px solid #d9d9d9;
  padding-left: 1em;
  margin-left: 0;
  margin-right: 0;
  color: #666;
}

.markdown-content img {
  max-width: 100%;
  height: auto;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #d9d9d9;
  padding: 8px;
}

.markdown-content table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

/* 聊天界面的自定义样式 */
.ant-list-item {
  transition: background-color 0.3s ease;
}

.ant-list-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.message-card {
  transition: transform 0.2s ease;
}

.message-card:hover {
  transform: translateY(-1px);
}

.avatar-user {
  background-color: #1890ff;
}

.avatar-assistant {
  background-color: #f56a00;
}

/* 侧边栏对话历史记录样式 */
.history-item {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.history-item:hover {
  background-color: #f0f0f0;
}

.history-item-active {
  background-color: #e6f7ff;
  border-right: 3px solid #1890ff;
}

.history-item-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.history-item-preview {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #8c8c8c;
  font-size: 12px;
}

/* 侧边栏折叠样式 */
.ant-layout-sider-collapsed .history-item-title,
.ant-layout-sider-collapsed .history-item-preview {
  display: none;
}

.ant-layout-sider-collapsed .ant-btn-text {
  padding: 4px;
}

/* 定制滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计调整 */
@media (max-width: 768px) {
  .markdown-content pre {
    padding: 8px;
  }
  
  .ant-card-body {
    padding: 12px;
  }
}
