import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout, ConfigProvider } from 'antd';
import { useAuth } from './hooks/useAuth';
import Navbar from './components/Navbar';
import Chat from './pages/Chat';
import ApiKeys from './pages/ApiKeys';
import Credits from './pages/Credits';
import LLMConfig from './pages/LLMConfig';
import MCPServers from './pages/MCPServers';
import AgentPods from './pages/AgentPods';
import Login from './pages/Login';
import Callback from './pages/Callback';
import TestLogin from './pages/TestLogin';
import { useEffect, useState } from 'react';
import { initializeDevTools, enableDevMode, disableDevMode, checkDevelopmentMode, forceInitStageWiseToolbar } from './utils/devtools';

// 在React应用完全挂载后初始化开发工具
// 不在模块顶层立即执行，而是在组件挂载后执行

// 将开发工具函数挂载到全局，方便在控制台中使用
if (typeof window !== 'undefined') {
  (window as any).devTools = {
    enable: enableDevMode,
    disable: disableDevMode,
    check: checkDevelopmentMode,
    init: initializeDevTools,
    forceInit: forceInitStageWiseToolbar
  };
  
  // 如果是开发模式，在控制台显示帮助信息
  if (checkDevelopmentMode()) {
    console.log(`
🔧 开发工具已启用！
可以在控制台中使用以下命令：
- devTools.enable()    - 手动启用开发模式
- devTools.disable()   - 手动禁用开发模式  
- devTools.check()     - 检查当前是否为开发模式
- devTools.init()      - 重新初始化开发工具
- devTools.forceInit() - 强制初始化StagWise Toolbar (解决容器尺寸问题)
    `);
  }
}

const { Content } = Layout;

// 登录重定向组件
const LoginRedirect = () => {
  useEffect(() => {
    // 直接重定向到登录API
    window.location.href = '/api/v1/login';
  }, []);

  return (
    <div style={{ textAlign: 'center', marginTop: '20vh' }}>
      <p>正在跳转到登录页面...</p>
    </div>
  );
};

function App() {
  const { user, loading } = useAuth();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 在App组件挂载后初始化开发工具
  useEffect(() => {
    // 使用 requestAnimationFrame 确保在下一帧执行，此时DOM已经完全渲染
    const timeoutId = setTimeout(() => {
      requestAnimationFrame(() => {
        initializeDevTools().catch(error => {
          console.warn('Development tools initialization failed:', error);
        });
      });
    }, 100); // 给React一些时间完成初始渲染

    return () => clearTimeout(timeoutId);
  }, []); // 只在组件挂载时执行一次

  // 根据窗口大小设置Content的padding
  const getContentPadding = () => {
    if (windowWidth < 576) return '12px'; // 手机
    if (windowWidth < 992) return '16px'; // 平板
    return '24px'; // 桌面
  };

  // 如果正在加载认证状态，显示一个简单的加载指示器
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <p>加载中...</p>
      </div>
    );
  }

  return (
    <ConfigProvider
      theme={{
        token: {
          borderRadius: 6,
        },
        components: {
          Layout: {
            bodyBg: '#f5f5f5',
          }
        }
      }}
    >
      <Layout style={{ minHeight: '100vh', height: '100vh', width: '100vw', display: 'flex', flexDirection: 'column' }}>
        {user && <Navbar />}
        <Content style={{
          padding: getContentPadding(),
          width: '100%',
          flex: '1',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'auto'
        }}>
          <Routes>
            {/* 登录页面 - 已登录用户将被重定向到控制台 */}
            <Route path="/login" element={!user ? <Login /> : <Navigate to="/console" replace />} />
            {/* /console/login 直接重定向到登录API */}
            <Route path="/console/login" element={
              !user ? <LoginRedirect /> : <Navigate to="/console" replace />
            } />

            {/* 主页 - 需要登录 */}
            <Route
              path="/"
              element={
                window.location.search.includes('token=')
                  ? <div>重定向中...</div> // 如果根路径带有token参数，显示临时内容
                  : user
                    ? <Navigate to="/console" replace />
                    : <Navigate to="/login" replace />
              }
            />

            {/* Console主页 - 需要登录 */}
            <Route
              path="/console"
              element={user ? <Navigate to="/console/chat" replace /> : <Navigate to="/login" replace />}
            />

            {/* Chat with conversation ID - 需要登录 */}
            <Route
              path="/console/chat/:conversationId"
              element={user ? <Chat /> : <Navigate to="/login" replace />}
            />

            {/* Chat without conversation ID - 需要登录 */}
            <Route
              path="/console/chat"
              element={user ? <Chat /> : <Navigate to="/login" replace />}
            />

            {/* API密钥页面 - 需要登录 */}
            <Route
              path="/console/api-keys"
              element={user ? <ApiKeys /> : <Navigate to="/login" replace />}
            />

            {/* 积分页面 - 需要登录 */}
            <Route
              path="/console/credits"
              element={user ? <Credits /> : <Navigate to="/login" replace />}
            />

            {/* LLM提供商配置页面 - 需要登录 */}
            <Route
              path="/console/llm-config"
              element={user ? <LLMConfig /> : <Navigate to="/login" replace />}
            />

            {/* MCP服务器配置页面 - 需要登录 */}
            <Route
              path="/console/mcp-servers"
              element={user ? <MCPServers /> : <Navigate to="/login" replace />}
            />

            {/* Agent Pod管理页面 - 需要登录 */}
            <Route
              path="/console/agent-pods"
              element={user ? <AgentPods /> : <Navigate to="/login" replace />}
            />

            {/* 回调页面 - 处理认证回调 */}
            <Route path="/callback" element={<Callback />} />

            {/* 测试登录页面 - 直接显示，无需认证 */}
            <Route path="/test-login" element={<TestLogin />} />
            <Route path="/console/test-login" element={<TestLogin />} />
          </Routes>
        </Content>
      </Layout>
    </ConfigProvider>
  );
}

export default App; 