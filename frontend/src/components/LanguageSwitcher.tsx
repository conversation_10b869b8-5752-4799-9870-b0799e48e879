import { Dropdown, Button } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { MenuProps } from 'antd';

interface LanguageSwitcherProps {
  iconOnly?: boolean;
  buttonStyle?: React.CSSProperties;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ 
  iconOnly = false, 
  buttonStyle = {} 
}) => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    // 保存语言选择到本地存储
    localStorage.setItem('i18nextLng', lng);
  };

  const languageItems: MenuProps['items'] = [
    {
      key: 'en',
      label: 'English',
      onClick: () => changeLanguage('en'),
    },
    {
      key: 'zh',
      label: '中文',
      onClick: () => changeLanguage('zh'),
    },
  ];

  return (
    <Dropdown menu={{ items: languageItems }} placement="bottomRight">
      <Button 
        type="text" 
        icon={<GlobalOutlined />}
        style={{ ...buttonStyle }}
      >
        {!iconOnly && (i18n.language === 'zh' ? '中文' : 'English')}
      </Button>
    </Dropdown>
  );
};

export default LanguageSwitcher; 