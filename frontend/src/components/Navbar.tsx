import { useState, useEffect } from 'react';
import { Layout, <PERSON>u, But<PERSON>, Drawer, Spin, Tooltip, Dropdown } from 'antd';
import { 
  MessageOutlined, 
  KeyOutlined, 
  CreditCardOutlined, 
  LogoutOutlined,
  MenuOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  CloudServerOutlined,
  InfoCircleOutlined,
  DeploymentUnitOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './LanguageSwitcher';
import VERSION from '../version';

const { Header } = Layout;

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout } = useAuth();
  const { t, i18n } = useTranslation();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [docLinks, setDocLinks] = useState<{ mainDoc: string }>({ mainDoc: '' });
  const [loadingLinks, setLoadingLinks] = useState(true);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 获取文档链接
  useEffect(() => {
    const fetchDocLinks = async () => {
      try {
        setLoadingLinks(true);
        const currentLang = i18n.language || 'en';
        const response = await axios.get(`/api/v1/doc-links?lang=${currentLang}`);
        setDocLinks(response.data.links);
      } catch (error) {
        console.error('Failed to fetch doc links:', error);
        // 如果获取失败，保持空状态，不设置硬编码的后备值
        // 后端应该总是返回有效的文档链接
      } finally {
        setLoadingLinks(false);
      }
    };

    fetchDocLinks();
  }, [i18n.language]); // 当语言改变时重新获取

  // 移动设备视图的断点
  const isMobile = windowWidth < 768;

  const menuItems = [
    {
      key: '/console/chat',
      icon: <MessageOutlined />,
      label: t('navbar.chat'),
    },
    {
      key: '/console/api-keys',
      icon: <KeyOutlined />,
      label: t('navbar.apiKeys'),
    },
    {
      key: '/console/llm-config',
      icon: <SettingOutlined />,
      label: t('navbar.llmConfig'),
    },
    {
      key: '/console/mcp-servers',
      icon: <CloudServerOutlined />,
      label: t('navbar.mcpServers'),
    },
    {
      key: '/console/agent-pods',
      icon: <DeploymentUnitOutlined />,
      label: t('navbar.agentPods'),
    },
    {
      key: '/console/credits',
      icon: <CreditCardOutlined />,
      label: t('navbar.credits'),
    },
  ];

  const docItem = {
    key: 'docs',
    icon: loadingLinks ? <Spin size="small" /> : <QuestionCircleOutlined />,
    label: t('navbar.documentation'),
    onClick: () => window.open(docLinks.mainDoc, '_blank'),
    disabled: loadingLinks || !docLinks.mainDoc,
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('navbar.logout'),
      onClick: logout,
    },
  ];

  const userMenu = (
    <Menu
      items={userMenuItems}
      onClick={({ key }) => {
        const item = userMenuItems.find(item => item.key === key);
        if (item && item.onClick) {
          item.onClick();
        }
      }}
      style={{
        backgroundColor: '#fafafa',
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
      }}
    />
  );

  const logoutItem = {
    key: 'logout',
    icon: <LogoutOutlined />,
    label: t('navbar.logout'),
    onClick: logout,
  };

  // 移动视图的菜单，包含所有项
  const mobileMenuItems = [...menuItems, docItem, logoutItem];

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key === 'logout') {
      logout();
    } else if (key === 'docs') {
      if (docLinks.mainDoc) {
        window.open(docLinks.mainDoc, '_blank');
      }
    } else {
      navigate(key);
      setDrawerVisible(false);
    }
  };

  // 检查是否在聊天页面 (包括带conversation ID的页面)
  const isChatPage = location.pathname === '/console' || 
                     location.pathname === '/console/chat' || 
                     location.pathname.startsWith('/console/chat/');

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    if (isChatPage) {
      return ['/console/chat'];
    }
    return [location.pathname];
  };

  return (
    <Header style={{ 
      display: 'flex', 
      alignItems: 'center', 
      padding: isMobile ? '0 16px' : '0 24px',
      position: 'sticky',
      top: 0,
      zIndex: 1010,
      width: '100%',
      height: '64px',
      flexShrink: 0,
      backgroundColor: '#f8f9fa',
      borderBottom: '1px solid #e0e0e0',
      boxShadow: '0 1px 4px rgba(0,0,0,0.08)'
    }}>
      <div style={{ 
        color: '#616161', 
        fontSize: isMobile ? '18px' : '20px', 
        marginRight: isMobile ? '16px' : '24px',
        whiteSpace: 'nowrap',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        fontWeight: 600
      }}>
        {t('navbar.title')}
        <Tooltip title={`Frontend: ${VERSION.full}\nBuild Time: ${VERSION.buildTime}`}>
          <InfoCircleOutlined style={{ fontSize: '14px', opacity: 0.6, color: '#9e9e9e' }} />
        </Tooltip>
      </div>

      {isMobile ? (
        <>
          <div style={{ flex: 1 }}></div>
          <LanguageSwitcher 
            iconOnly={true} 
            buttonStyle={{ border: 'none', color: '#616161', marginRight: '8px' }} 
          />
          <Button
            type="text"
            icon={<MenuOutlined style={{ color: '#616161', fontSize: '18px' }} />}
            onClick={() => setDrawerVisible(true)}
            style={{ border: 'none' }}
          />
          <Drawer
            title={t('common.menu')}
            placement="right"
            onClose={() => setDrawerVisible(false)}
            open={drawerVisible}
            bodyStyle={{ padding: 0 }}
          >
            <Menu
              mode="vertical"
              selectedKeys={[location.pathname]}
              items={mobileMenuItems}
              onClick={handleMenuClick}
              style={{ borderRight: 'none' }}
            />
          </Drawer>
        </>
      ) : (
        <>
          <Menu
            mode="horizontal"
            selectedKeys={getSelectedKeys()}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ 
              flex: 1,
              backgroundColor: 'transparent',
              borderBottom: 'none',
              color: '#616161'
            }}
            theme="light"
          />
          <Button
            type="text"
            icon={loadingLinks ? <Spin size="small" /> : <QuestionCircleOutlined />}
            onClick={() => window.open(docLinks.mainDoc, '_blank')}
            disabled={loadingLinks || !docLinks.mainDoc}
            style={{
              border: 'none',
              color: '#616161',
              marginRight: '16px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            {t('navbar.documentation')}
          </Button>
          <LanguageSwitcher 
            iconOnly={true} 
            buttonStyle={{ border: 'none', color: '#616161', marginRight: '16px' }} 
          />
          <Dropdown 
            overlay={userMenu} 
            trigger={['click']} 
            placement="bottomRight"
          >
            <Button
              type="text"
              style={{
                border: 'none',
                padding: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                backgroundColor: '#e0e0e0',
                width: '40px',
                height: '40px'
              }}
              icon={<UserOutlined style={{ color: '#616161', fontSize: '18px' }} />}
            />
          </Dropdown>
        </>
      )}
    </Header>
  );
};

export default Navbar; 