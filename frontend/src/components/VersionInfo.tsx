import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Button, message, Space } from 'antd';
import { InfoCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import axios from 'axios';
import VERSION from '../version';

interface BackendVersion {
  major: string;
  gitCommit: string;
  buildTime: string;
  full: string;
}

const VersionInfo: React.FC = () => {
  const [backendVersion, setBackendVersion] = useState<BackendVersion | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchBackendVersion = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/v1/version');
      if (response.data.success) {
        setBackendVersion(response.data.data);
      }
    } catch (error) {
      message.error('Failed to fetch backend version');
      console.error('Error fetching backend version:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBackendVersion();
  }, []);

  return (
    <Card 
      title={
        <Space>
          <InfoCircleOutlined />
          Version Information
        </Space>
      }
      extra={
        <Button 
          icon={<ReloadOutlined />} 
          onClick={fetchBackendVersion}
          loading={loading}
          size="small"
        >
          Refresh
        </Button>
      }
      style={{ maxWidth: 600 }}
    >
      <Descriptions column={1} bordered size="small">
        <Descriptions.Item label="Frontend Version">
          {VERSION.full}
        </Descriptions.Item>
        <Descriptions.Item label="Frontend Build Time">
          {new Date(VERSION.buildTime).toLocaleString()}
        </Descriptions.Item>
        <Descriptions.Item label="Frontend Commit">
          {VERSION.gitCommit}
        </Descriptions.Item>
        <Descriptions.Item label="Backend Version">
          {backendVersion ? backendVersion.full : 'Loading...'}
        </Descriptions.Item>
        <Descriptions.Item label="Backend Build Time">
          {backendVersion ? new Date(backendVersion.buildTime).toLocaleString() : 'Loading...'}
        </Descriptions.Item>
        <Descriptions.Item label="Backend Commit">
          {backendVersion ? backendVersion.gitCommit : 'Loading...'}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default VersionInfo; 