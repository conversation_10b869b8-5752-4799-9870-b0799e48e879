<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StagWise Toolbar 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Monaco', 'Consolas', monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🛠️ StagWise Toolbar 测试页面</h1>
    
    <div class="info">
        <strong>测试目的：</strong>验证 StagWise Toolbar 在开发模式下是否能正确加载和初始化
    </div>

    <div id="status-container"></div>

    <h2>测试步骤</h2>
    <ol>
        <li>打开浏览器开发者工具的 Console 面板</li>
        <li>点击下面的测试按钮</li>
        <li>观察控制台输出和页面状态</li>
        <li>查看是否有 StagWise Toolbar 出现在页面上</li>
    </ol>

    <h2>控制面板</h2>
    <button onclick="testStagewiseToolbar()">🧪 测试 StagWise Toolbar 加载</button>
    <button onclick="checkDevelopmentMode()">🔍 检查开发模式</button>
    <button onclick="enableDevMode()">🔧 手动启用开发模式</button>
    <button onclick="clearStatus()">🧹 清空状态</button>

    <h2>预期结果</h2>
    <div class="code">
✅ 开发模式检测成功
✅ StagWise Toolbar 模块加载成功
✅ initToolbar 函数找到并执行
✅ 控制台输出：🛠️ StagWise Toolbar initialized successfully
✅ 页面上出现 StagWise Toolbar UI
    </div>

    <h2>调试信息</h2>
    <div id="debug-info"></div>

    <script type="module">
        // 导入我们的开发工具
        import { 
            checkDevelopmentMode, 
            loadStageWiseToolbar, 
            enableDevMode as enableDev,
            initializeDevTools 
        } from './src/utils/devtools.js';

        // 全局函数，供按钮调用
        window.testStagewiseToolbar = async function() {
            addStatus('开始测试 StagWise Toolbar...', 'info');
            
            try {
                await loadStageWiseToolbar();
                addStatus('StagWise Toolbar 测试完成，请检查控制台输出', 'success');
            } catch (error) {
                addStatus(`测试失败: ${error.message}`, 'error');
                console.error('StagWise Toolbar 测试失败:', error);
            }
        };

        window.checkDevelopmentMode = function() {
            const isDev = checkDevelopmentMode();
            addStatus(`开发模式检测结果: ${isDev ? '✅ 开发模式' : '❌ 非开发模式'}`, isDev ? 'success' : 'warning');
            
            // 显示详细调试信息
            const debugInfo = {
                'Vite DEV': window.location.port === '5173' || import.meta?.env?.DEV,
                'Localhost': window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1',
                'Dev Port': ['5173', '3000', '8080'].includes(window.location.port),
                'Dev Param': window.location.search.includes('dev=true'),
                'LocalStorage Flag': localStorage.getItem('dev-mode') === 'true',
                'Current URL': window.location.href,
                'Current Port': window.location.port || '80/443'
            };
            
            document.getElementById('debug-info').innerHTML = 
                '<h3>调试信息</h3>' + 
                Object.entries(debugInfo)
                    .map(([key, value]) => `<div><strong>${key}:</strong> ${value}</div>`)
                    .join('');
        };

        window.enableDevMode = function() {
            enableDev();
            addStatus('开发模式已手动启用，正在重新初始化...', 'info');
            setTimeout(() => {
                window.checkDevelopmentMode();
                initializeDevTools();
            }, 100);
        };

        window.clearStatus = function() {
            document.getElementById('status-container').innerHTML = '';
            document.getElementById('debug-info').innerHTML = '';
        };

        function addStatus(message, type) {
            const container = document.getElementById('status-container');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(div);
            
            // 控制台也输出
            console.log(`[StagWise Test] ${message}`);
        }

        // 页面加载时自动检查开发模式
        window.addEventListener('load', () => {
            addStatus('页面加载完成', 'info');
            window.checkDevelopmentMode();
            
            // 自动初始化开发工具
            addStatus('自动初始化开发工具...', 'info');
            initializeDevTools().then(() => {
                addStatus('开发工具初始化完成', 'success');
            }).catch(error => {
                addStatus(`开发工具初始化失败: ${error.message}`, 'error');
            });
        });
    </script>
</body>
</html> 