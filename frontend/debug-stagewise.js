// 独立的 StagWise Toolbar 调试脚本
// 可以在浏览器控制台中直接运行

console.log('🔍 开始调试 StagWise Toolbar...');

// 步骤1: 检查模块是否可以导入
async function debugStagewiseImport() {
    try {
        console.log('📦 正在导入 @stagewise/toolbar...');
        
        // 尝试导入模块
        const module = await import('@stagewise/toolbar');
        
        console.log('✅ 模块导入成功!');
        console.log('📋 可用的导出:', Object.keys(module));
        console.log('🔍 模块详情:', module);
        
        // 检查 initToolbar 函数
        if (module.initToolbar) {
            console.log('✅ 找到 initToolbar 函数');
            console.log('🔧 initToolbar 类型:', typeof module.initToolbar);
            
            // 尝试调用 initToolbar
            try {
                console.log('🚀 正在初始化 StagWise Toolbar...');
                module.initToolbar({
                    plugins: []
                });
                console.log('🎉 StagWise Toolbar 初始化成功!');
                
                // 检查是否有 UI 元素被添加到页面
                setTimeout(() => {
                    const stageElements = document.querySelectorAll('[data-stagewise], [class*="stagewise"], [id*="stagewise"]');
                    if (stageElements.length > 0) {
                        console.log('🎯 发现 StagWise UI 元素:', stageElements);
                    } else {
                        console.log('⚠️ 未发现 StagWise UI 元素，可能需要时间加载');
                    }
                }, 1000);
                
            } catch (initError) {
                console.error('❌ 初始化失败:', initError);
            }
        } else {
            console.error('❌ 未找到 initToolbar 函数');
            console.log('🔍 尝试查找其他可能的初始化函数...');
            
            // 检查其他可能的函数
            const possibleInits = ['init', 'initialize', 'start', 'load', 'setup'];
            possibleInits.forEach(funcName => {
                if (module[funcName] && typeof module[funcName] === 'function') {
                    console.log(`✅ 找到可能的初始化函数: ${funcName}`);
                }
            });
        }
        
        return module;
        
    } catch (error) {
        console.error('❌ 模块导入失败:', error);
        console.log('💡 可能的原因:');
        console.log('   1. @stagewise/toolbar 包未正确安装');
        console.log('   2. 模块路径不正确');
        console.log('   3. TypeScript 配置问题');
        console.log('   4. 构建工具配置问题');
        return null;
    }
}

// 步骤2: 检查开发环境
function debugDevelopmentMode() {
    console.log('🔍 检查开发环境...');
    
    const checks = {
        'import.meta.env.DEV': typeof import !== 'undefined' && import.meta?.env?.DEV,
        'localhost': window.location.hostname === 'localhost',
        '127.0.0.1': window.location.hostname === '127.0.0.1',
        'port 5173 (Vite)': window.location.port === '5173',
        'port 3000 (React)': window.location.port === '3000',
        'port 8080 (Backend)': window.location.port === '8080',
        'dev=true param': window.location.search.includes('dev=true'),
        'localStorage dev-mode': localStorage.getItem('dev-mode') === 'true'
    };
    
    console.log('📊 开发模式检查结果:');
    Object.entries(checks).forEach(([check, result]) => {
        console.log(`   ${result ? '✅' : '❌'} ${check}: ${result}`);
    });
    
    const isDev = Object.values(checks).some(Boolean);
    console.log(`🎯 综合判断: ${isDev ? '✅ 开发模式' : '❌ 非开发模式'}`);
    
    return isDev;
}

// 步骤3: 执行完整测试
async function debugComplete() {
    console.log('🚀 开始完整调试...');
    console.log('=' .repeat(50));
    
    // 检查开发模式
    const isDev = debugDevelopmentMode();
    console.log('=' .repeat(50));
    
    if (isDev) {
        // 尝试导入和初始化
        await debugStagewiseImport();
    } else {
        console.log('⚠️ 当前不是开发模式，StagWise Toolbar 不会加载');
        console.log('💡 要强制启用，请运行: localStorage.setItem("dev-mode", "true")');
    }
    
    console.log('=' .repeat(50));
    console.log('🔧 调试完成! 如有问题，请检查上述输出');
}

// 暴露调试函数到全局
window.debugStagewise = {
    import: debugStagewiseImport,
    devMode: debugDevelopmentMode,
    complete: debugComplete,
    enableDev: () => {
        localStorage.setItem('dev-mode', 'true');
        console.log('✅ 开发模式已启用');
    }
};

console.log('🛠️ 调试脚本已加载!');
console.log('📖 使用方法:');
console.log('   debugStagewise.complete()    - 运行完整调试');
console.log('   debugStagewise.import()      - 仅测试模块导入');
console.log('   debugStagewise.devMode()     - 仅检查开发模式');
console.log('   debugStagewise.enableDev()   - 强制启用开发模式');

// 自动运行完整调试
debugComplete(); 