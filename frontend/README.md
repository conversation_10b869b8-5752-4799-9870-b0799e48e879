# AI Dashboard Frontend

This is the frontend application for the AI Dashboard, built with React and Ant Design.

## Prerequisites

- Node.js 18+
- npm

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```
VITE_API_URL=http://localhost:8080
```

Note: Authentication is handled entirely by the backend. The frontend only needs to know the API URL.

## Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

## Features

- AI Chat Interface
  - Real-time messaging
  - Multiple chat sessions
  - Markdown support
  - Code highlighting
  - Copy to clipboard

- API Key Management
  - Create and revoke API keys
  - View key usage
  - Copy keys to clipboard

- Credit System
  - View credit balance
  - Transaction history
  - Add credits

## Project Structure

```
src/
├── components/     # Reusable components
├── hooks/         # Custom React hooks
├── pages/         # Page components
├── services/      # API services
└── utils/         # Utility functions
```

## Development

To start the development server:

```bash
npm run dev
```

## Development Tools

The application includes automatic loading of StagWise Toolbar in development mode.

### Automatic Detection

The development mode is automatically detected based on:

1. **Vite Environment**: `import.meta.env.DEV` (most reliable)
2. **Local Environment**: localhost, 127.0.0.1, or domains containing "local"
3. **Development Ports**: 5173 (Vite), 3000 (React), 8080 (Backend)
4. **URL Parameters**: `?dev=true` or `?debug=true`
5. **localStorage Flag**: `dev-mode=true`
6. **Backend API**: `/api/v1/env` endpoint (optional, with timeout)

### Manual Control

You can manually control development mode in the browser console:

```javascript
// Check if development mode is enabled
devTools.check()

// Enable development mode manually
devTools.enable()

// Disable development mode
devTools.disable()

// Reinitialize development tools
devTools.init()
```

### StagWise Toolbar

The [StagWise Toolbar](https://www.stagewise.ai/) is automatically loaded in development mode to provide:

- Performance monitoring
- Network request inspection
- Component debugging
- State management tools

The toolbar will only load when running in development mode and won't be included in production builds.

## Building

To build for production:

```bash
npm run build
```

## Environment Variables

The application supports the following environment variables:

- `DEV`: Set by Vite to indicate development mode
- `PROD`: Set by Vite to indicate production mode
- `MODE`: Current mode (development/production)

## Architecture

The application uses:

- **React 18** with TypeScript
- **Ant Design** for UI components
- **i18next** for internationalization
- **React Router** for routing
- **Vite** for build tooling

### Key Files

- `src/App.tsx`: Main application component with routing
- `src/utils/devtools.ts`: Development tools utilities
- `src/main.tsx`: Application entry point
- `vite.config.ts`: Vite configuration with proxy setup

## Development vs Production

### Development Mode Features

- StagWise Toolbar automatically loaded
- Console debug information
- Development server proxy to backend
- Hot reload enabled
- Detailed error messages

### Production Mode Features

- Optimized bundle size
- No development tools loaded
- Minified assets
- Security headers
- Performance optimizations

The application automatically detects the environment and adjusts behavior accordingly. 