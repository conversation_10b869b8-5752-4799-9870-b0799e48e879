{"name": "frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@stagewise/toolbar": "^0.3.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.5", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.5.0", "typescript": "~5.8.3", "vite": "^6.3.5"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@logto/browser": "^3.0.8", "@monaco-editor/react": "^4.7.0", "@types/react-router-dom": "^5.3.3", "antd": "^5.25.3", "axios": "^1.9.0", "i18next": "^23.10.2", "i18next-browser-languagedetector": "^7.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^14.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.12.3", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "zustand": "^5.0.5"}}