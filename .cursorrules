# Cursor Rules for Animus Web Project

## Project Overview
This is a full-stack AI chat application with Go backend and React frontend, featuring user authentication, credit management, API keys, LLM provider management, and payment integration.

## Architecture
- **Backend**: Go with Gin framework, GORM for database ORM
- **Frontend**: React 18 with TypeScript, Ant Design, i18next for internationalization
- **Database**: PostgreSQL (via GORM)
- **Authentication**: OAuth-based with JWT tokens
- **Payment**: Modular payment system with XunhuPay integration

## Directory Structure
```
backend/
├── cmd/server/           # Main application entry point
├── internal/
│   ├── auth/            # Authentication logic
│   ├── config/          # Configuration management
│   ├── db/              # Database layer (interfaces + GORM implementations)
│   │   └── gorm/        # GORM implementations
│   ├── middleware/      # HTTP middleware
│   ├── models/          # Data models
│   ├── payment/         # Payment system
│   ├── server/          # HTTP handlers and managers
│   └── utils/           # Utility functions
frontend/
├── src/
│   ├── components/      # Reusable React components
│   ├── pages/           # Page components
│   ├── i18n/           # Internationalization
│   └── utils/          # Frontend utilities
```

## Coding Standards

### Go Backend Rules
1. **Package Naming**: Use lowercase, single words. Avoid underscores.
2. **Interface Design**: Define interfaces in the same package where they're used
3. **Error Handling**: Always handle errors explicitly, use wrapped errors with context
4. **Logging**: Use structured logging with appropriate log levels
5. **Database Layer**: 
   - Use GORM as the ORM
   - All database operations should go through repository interfaces
6. **API Design**:
   - RESTful endpoints under `/api/v1/`
   - Use Gin framework conventions
   - Validate input using struct tags
   - Return consistent JSON responses

### Frontend React Rules
1. **Component Structure**: Use functional components with hooks
2. **TypeScript**: Always use TypeScript, define proper interfaces
3. **Styling**: Use Ant Design components and CSS modules
4. **State Management**: Use React hooks (useState, useEffect, useContext)
5. **API Calls**: Use axios with proper error handling
6. **Internationalization**: Use react-i18next for all user-facing text

## Database Patterns

### Repository Pattern
```go
// Define interface in db/interfaces.go
type UserRepository interface {
    GetUser(id string) (*models.User, error)
    CreateUser(user *models.User) error
    // ... other methods
}

// Implement in db/gorm/user_repository.go
type UserRepository struct{}

func (r *UserRepository) GetUser(id string) (*models.User, error) {
    // GORM implementation
}
```

### Database Migration
- Use GORM auto-migration for development
- Create explicit migration files for production
- Never delete data in migrations

## API Design Patterns

### Handler Structure
```go
type Manager struct {
    repository SomeRepository
}

func (m *Manager) GetSomething(c *gin.Context) {
    // 1. Extract user ID from context
    userID, err := m.GetUserIDFromContext(c)
    if err != nil {
        return
    }
    
    // 2. Validate input
    var request SomeRequest
    if err := c.ShouldBindJSON(&request); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
        return
    }
    
    // 3. Business logic
    result, err := m.repository.DoSomething(userID, request)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal error"})
        return
    }
    
    // 4. Return response
    c.JSON(http.StatusOK, gin.H{"success": true, "data": result})
}
```

### Authentication Middleware
- All protected routes must use `middleware.AuthMiddleware()`
- User ID is stored in context as "user_id"
- Extract user ID using helper methods

## Payment System Rules

### Provider Pattern
```go
type PaymentProvider interface {
    CreatePayment(req *PaymentRequest) (*PaymentResponse, error)
    QueryPayment(orderID string) (*PaymentNotification, error)
    VerifyNotification(params map[string]string) (*PaymentNotification, error)
    GetName() string
}
```

### Security Requirements
- Always verify payment notification signatures
- Use HTTPS for all payment callbacks in production
- Log all payment operations for audit
- Validate user permissions for payment queries

## Frontend Patterns

### Page Component Structure
```tsx
const SomePage: React.FC = () => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<SomeType[]>([]);

    useEffect(() => {
        loadData();
    }, []);

    const loadData = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/api/v1/something');
            setData(response.data.data);
        } catch (error) {
            handleApiError(error, t);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div>
            {/* Component JSX */}
        </div>
    );
};
```

### Error Handling
- Use `handleApiError` utility for consistent error handling
- Show user-friendly error messages
- Log errors to console for debugging

### Internationalization
- All user-facing text must use `t()` function
- Define translations in both `zh.ts` and `en.ts`
- Group translations by feature/page

## Security Guidelines

### Backend Security
- Validate all input data
- Use parameterized queries (GORM handles this)
- Implement rate limiting for sensitive endpoints
- Log security events
- Never expose sensitive data in API responses

### Frontend Security
- Store auth tokens securely
- Validate user permissions before showing UI elements
- Sanitize user input
- Use HTTPS in production

## Testing Guidelines

### Backend Testing
- Write unit tests for business logic
- Test repository interfaces with mock implementations
- Integration tests for API endpoints
- Test error scenarios

### Frontend Testing
- Component unit tests
- Integration tests for user workflows
- Accessibility testing
- Cross-browser compatibility

## Performance Guidelines

### Backend Performance
- Use database indexes appropriately
- Implement pagination for list endpoints
- Cache frequently accessed data
- Monitor database query performance

### Frontend Performance
- Lazy load components when possible
- Optimize bundle size
- Use React.memo for expensive components
- Implement proper loading states

## Deployment Considerations

### Environment Configuration
- Use environment variables for all configuration
- Never commit secrets to version control
- Different configs for dev/staging/production

### Database Considerations
- Use connection pooling
- Monitor database performance
- Regular backups
- Database migration strategy

## Code Review Checklist

### Backend
- [ ] Error handling implemented
- [ ] Input validation present
- [ ] Logging added for important operations
- [ ] Database operations use repository pattern
- [ ] Authentication checked for protected routes
- [ ] Tests written and passing

### Frontend
- [ ] TypeScript types defined
- [ ] Loading states implemented
- [ ] Error handling present
- [ ] Internationalization used
- [ ] Responsive design considered
- [ ] Accessibility guidelines followed

## Common Patterns to Follow

### Database Transactions
```go
err := db.Transaction(func(tx *gorm.DB) error {
    // Multiple operations that should be atomic
    return nil
})
```

### API Response Format
```go
// Success response
c.JSON(http.StatusOK, gin.H{
    "success": true,
    "data": result,
})

// Error response
c.JSON(http.StatusBadRequest, gin.H{
    "error": "Error message",
})
```

### React Hook Pattern
```tsx
const useSomeData = () => {
    const [data, setData] = useState<SomeType[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const loadData = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            const result = await api.getData();
            setData(result);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, []);

    return { data, loading, error, loadData };
};
```

## Debugging Guidelines

### Backend Debugging
- Use structured logging with context
- Include request IDs in logs
- Log database queries in development
- Use proper log levels (DEBUG, INFO, WARN, ERROR)

### Frontend Debugging
- Use browser dev tools
- Add console.log for development debugging
- Use React Developer Tools
- Monitor network requests

## Dependencies Management

### Backend Dependencies
- Keep go.mod clean and up to date
- Use semantic versioning
- Regular security updates
- Document any specific version requirements

### Frontend Dependencies
- Regular npm audit and updates
- Use exact versions for critical dependencies
- Document breaking changes
- Keep bundle size reasonable

Remember: Always prioritize code readability, maintainability, and security over cleverness. 每次变更优化记录写到changelog文件里