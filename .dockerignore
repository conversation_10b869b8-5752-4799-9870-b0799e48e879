# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
CHANGELOG.md
README.md
TESTING_GUIDE.md
UI_IMPROVEMENTS.md
PAYMENT_IMPLEMENTATION.md
AGENT_PODS.md

# Development files
.env
.env.local
.env.development
.env.test
.env.production

# Node.js
frontend/node_modules
frontend/.npm
frontend/.cache
frontend/coverage
frontend/.nyc_output

# Go
backend/bin
backend/animus-server
backend/backend
backend/data.db
backend/*.exe
backend/*.dll
backend/*.so
backend/*.dylib
backend/vendor

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Development scripts
*.ps1
*.sh
dev.ps1
start-dev.ps1
start.ps1
start.sh
frontend/dev.ps1
frontend/run-dev.ps1
backend/dev.ps1
backend/debug.ps1

# Build artifacts (will be built in Docker)
frontend/dist
backend/bin

# Logs
*.log
logs

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Package lock files (will be copied separately)
package-lock.json
