#!/bin/bash

# Testing startup script for Animus Web Application

echo "Starting Animus Web Application in Testing Mode..."

# Set environment variables for testing
export APP_ENV=testing
export GIN_MODE=test
export LOG_LEVEL=warn
export PORT=8081
export TEST_ENDPOINTS=true
export MOCK_SERVICES=true
export FAST_MODE=true

# Use in-memory database for testing
export DB_DSN="file::memory:?cache=shared"

echo "Environment: $APP_ENV"
echo "Port: ${PORT:-8081}"
echo "Log Level: ${LOG_LEVEL:-warn}"

# Build and run the application
echo "Building application for testing..."
go build -o animus-server-test ./cmd/server

if [ $? -eq 0 ]; then
    echo "Build successful. Starting test server..."
    ./animus-server-test
else
    echo "Build failed!"
    exit 1
fi
