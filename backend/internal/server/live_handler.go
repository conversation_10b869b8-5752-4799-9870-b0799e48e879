package server

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/animus-server/internal/config"
	"github.com/animus-server/internal/db"
	"github.com/animus-server/internal/llm"
	"github.com/animus-server/internal/models"
	"github.com/animus-server/internal/utils"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// LiveAPIManager manages Live API WebSocket connections
type LiveAPIManager struct {
	llmProviderRepo db.LLMProviderRepository
}

// NewLiveAPIManager creates a new Live API manager
func NewLiveAPIManager(llmProviderRepo db.LLMProviderRepository) *LiveAPIManager {
	return &LiveAPIManager{
		llmProviderRepo: llmProviderRepo,
	}
}

// Live API message types
type LiveMessage struct {
	Setup         *BidiGenerateContentSetup         `json:"setup,omitempty"`
	ClientContent *BidiGenerateContentClientContent `json:"clientContent,omitempty"`
	RealtimeInput *BidiGenerateContentRealtimeInput `json:"realtimeInput,omitempty"`
	ToolResponse  *BidiGenerateContentToolResponse  `json:"toolResponse,omitempty"`
}

type BidiGenerateContentSetup struct {
	Model             string           `json:"model"`
	GenerationConfig  GenerationConfig `json:"generationConfig"`
	SystemInstruction string           `json:"systemInstruction,omitempty"`
	Tools             []interface{}    `json:"tools,omitempty"`
}

type GenerationConfig struct {
	CandidateCount     int                    `json:"candidateCount,omitempty"`
	MaxOutputTokens    int                    `json:"maxOutputTokens,omitempty"`
	Temperature        float64                `json:"temperature,omitempty"`
	TopP               float64                `json:"topP,omitempty"`
	TopK               int                    `json:"topK,omitempty"`
	ResponseModalities []string               `json:"responseModalities,omitempty"`
	SpeechConfig       map[string]interface{} `json:"speechConfig,omitempty"`
}

type BidiGenerateContentClientContent struct {
	Turns        []Turn `json:"turns"`
	TurnComplete bool   `json:"turnComplete"`
}

type BidiGenerateContentRealtimeInput struct {
	MediaChunks []MediaChunk `json:"mediaChunks,omitempty"`
}

type BidiGenerateContentToolResponse struct {
	FunctionResponses []FunctionResponse `json:"functionResponses"`
}

type Turn struct {
	Role  string `json:"role"`
	Parts []Part `json:"parts"`
}

type Part struct {
	Text string `json:"text,omitempty"`
}

type MediaChunk struct {
	MimeType string `json:"mimeType"`
	Data     string `json:"data"`
}

type FunctionResponse struct {
	Name     string      `json:"name"`
	Response interface{} `json:"response"`
}

// Server response types
type LiveServerMessage struct {
	ServerContent *ServerContent `json:"serverContent,omitempty"`
	ToolCall      *ToolCall      `json:"toolCall,omitempty"`
	UsageMetadata *UsageMetadata `json:"usageMetadata,omitempty"`
}

type ServerContent struct {
	ModelTurn    *ModelTurn `json:"modelTurn,omitempty"`
	TurnComplete bool       `json:"turnComplete,omitempty"`
}

type ModelTurn struct {
	Parts []Part `json:"parts"`
}

type ToolCall struct {
	FunctionCalls []FunctionCall `json:"functionCalls"`
}

type FunctionCall struct {
	Name string      `json:"name"`
	Args interface{} `json:"args"`
}

type UsageMetadata struct {
	PromptTokenCount   int `json:"promptTokenCount"`
	ResponseTokenCount int `json:"responseTokenCount"`
	TotalTokenCount    int `json:"totalTokenCount"`
}

// WebSocket connection
type LiveConnection struct {
	conn    *websocket.Conn
	userID  string
	setup   *BidiGenerateContentSetup
	manager *LiveAPIManager
	ctx     context.Context
	cancel  context.CancelFunc
}

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow all origins for development
	},
}

// HandleLiveAPI handles WebSocket connections for Live API
func (m *LiveAPIManager) HandleLiveAPI(c *gin.Context) {
	// Extract user ID from token
	userID := utils.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Upgrade to WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Failed to upgrade to WebSocket: %v", err)
		return
	}
	defer conn.Close()

	// Create Live connection
	ctx, cancel := context.WithCancel(context.Background())
	liveConn := &LiveConnection{
		conn:    conn,
		userID:  userID,
		manager: m,
		ctx:     ctx,
		cancel:  cancel,
	}

	log.Printf("Live API WebSocket connection established for user: %s", userID)

	// Handle messages
	liveConn.handleMessages()
}

// Handle incoming messages
func (lc *LiveConnection) handleMessages() {
	defer lc.cancel()

	for {
		select {
		case <-lc.ctx.Done():
			return
		default:
			var message LiveMessage
			err := lc.conn.ReadJSON(&message)
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("WebSocket read error: %v", err)
				}
				return
			}

			// Process message
			if err := lc.processMessage(&message); err != nil {
				log.Printf("Error processing message: %v", err)
				lc.sendError(err.Error())
			}
		}
	}
}

// Process different message types
func (lc *LiveConnection) processMessage(message *LiveMessage) error {
	if message.Setup != nil {
		return lc.handleSetup(message.Setup)
	}

	if message.ClientContent != nil {
		return lc.handleClientContent(message.ClientContent)
	}

	if message.RealtimeInput != nil {
		return lc.handleRealtimeInput(message.RealtimeInput)
	}

	if message.ToolResponse != nil {
		return lc.handleToolResponse(message.ToolResponse)
	}

	return fmt.Errorf("unknown message type")
}

// Handle setup message
func (lc *LiveConnection) handleSetup(setup *BidiGenerateContentSetup) error {
	lc.setup = setup
	log.Printf("Live API setup configured for model: %s", setup.Model)
	return nil
}

// Handle client content (text messages)
func (lc *LiveConnection) handleClientContent(content *BidiGenerateContentClientContent) error {
	if !content.TurnComplete {
		return nil // Wait for complete turn
	}

	// Extract text from turns
	var userText string
	for _, turn := range content.Turns {
		if turn.Role == "user" {
			for _, part := range turn.Parts {
				if part.Text != "" {
					userText += part.Text + " "
				}
			}
		}
	}

	userText = strings.TrimSpace(userText)
	if userText == "" {
		return fmt.Errorf("no text content found")
	}

	// Get available LLM providers (user + system providers)
	provider, err := lc.getAvailableProvider()
	if err != nil {
		return err
	}

	// Stream response
	return lc.streamResponse(userText, provider)
}

// getAvailableProvider returns the first available LLM provider (user or system)
func (lc *LiveConnection) getAvailableProvider() (*models.LLMProvider, error) {
	// First try to get user providers
	userProviders, err := lc.manager.llmProviderRepo.GetUserLLMProviders(lc.userID)
	if err == nil && len(userProviders) > 0 {
		// Return the first user provider (or default if available)
		for _, provider := range userProviders {
			if provider.IsDefault {
				return &provider, nil
			}
		}
		return &userProviders[0], nil
	}

	// If no user providers, fall back to system providers
	systemProviders := lc.getSystemProviders()
	if len(systemProviders) == 0 {
		return nil, fmt.Errorf("no LLM providers configured")
	}

	// Return the first enabled system provider
	return &systemProviders[0], nil
}

// getSystemProviders returns all available system providers from configuration
func (lc *LiveConnection) getSystemProviders() []models.LLMProvider {
	// Get configuration
	cfg := config.Get()
	if cfg == nil {
		log.Printf("Warning: Configuration not loaded, returning empty system providers")
		return []models.LLMProvider{}
	}

	// Convert configuration providers to models.LLMProvider
	var systemProviders []models.LLMProvider
	now := time.Now()

	for _, configProvider := range cfg.LLMProvider.SystemProviders {
		// Only include enabled providers
		if !configProvider.Enabled {
			continue
		}

		// Resolve API key - use environment variable if available, otherwise use configured value
		apiKey := configProvider.APIKey
		if apiKey == "system_managed" {
			// Try to get from environment based on provider type
			switch strings.ToLower(configProvider.Type) {
			case "openai":
				if envKey := os.Getenv("OPENAI_API_KEY"); envKey != "" {
					apiKey = envKey
				}
			case "anthropic":
				if envKey := os.Getenv("ANTHROPIC_API_KEY"); envKey != "" {
					apiKey = envKey
				}
			case "google":
				if envKey := os.Getenv("GOOGLE_API_KEY"); envKey != "" {
					apiKey = envKey
				}
			}
		}

		provider := models.LLMProvider{
			ID:               configProvider.ID,
			UserID:           "system",
			Name:             configProvider.Name,
			Alias:            configProvider.Alias,
			Type:             configProvider.Type,
			APIKey:           apiKey,
			BaseURL:          configProvider.BaseURL,
			Model:            configProvider.Model,
			SystemPrompt:     configProvider.SystemPrompt,
			IsDefault:        configProvider.IsDefault,
			Temperature:      configProvider.Temperature,
			MaxTokens:        configProvider.MaxTokens,
			TopP:             configProvider.TopP,
			FrequencyPenalty: configProvider.FrequencyPenalty,
			PresencePenalty:  configProvider.PresencePenalty,
			CreatedAt:        now,
			UpdatedAt:        now,
		}

		systemProviders = append(systemProviders, provider)
	}

	return systemProviders
}

// Stream response from LLM
func (lc *LiveConnection) streamResponse(prompt string, provider *models.LLMProvider) error {
	// Create LLM client
	llmClient := llm.NewClient(provider)

	// Prepare messages for LLM
	messages := []llm.ChatMessage{}

	// Add system prompt if available
	if provider.SystemPrompt != "" {
		messages = append(messages, llm.ChatMessage{
			Role:    "system",
			Content: provider.SystemPrompt,
		})
	}

	// Add user message
	messages = append(messages, llm.ChatMessage{
		Role:    "user",
		Content: prompt,
	})

	// Create request
	request := llm.ChatRequest{
		Messages: messages,
	}

	// Create response channel
	responseChan := make(chan llm.StreamResponse, 100)

	// Start streaming in a goroutine
	go func() {
		if err := llmClient.ChatStream(lc.ctx, request, responseChan); err != nil {
			log.Printf("Error in LLM streaming: %v", err)
		}
	}()

	// Track accumulated content for this streaming session
	var accumulatedContent strings.Builder
	isFirstChunk := true

	// Process streaming responses
	for {
		select {
		case <-lc.ctx.Done():
			return nil
		case response, ok := <-responseChan:
			if !ok {
				// Channel closed, streaming finished
				// Send final turn complete message
				completeMessage := &LiveServerMessage{
					ServerContent: &ServerContent{
						TurnComplete: true,
					},
				}
				if err := lc.conn.WriteJSON(completeMessage); err != nil {
					return err
				}
				return nil
			}

			// Send delta if available
			if response.Delta != "" {
				// Accumulate content
				accumulatedContent.WriteString(response.Delta)

				// Create server content with delta for incremental update
				part := Part{Text: response.Delta}
				serverContent := &ServerContent{
					ModelTurn: &ModelTurn{
						Parts: []Part{part},
					},
					TurnComplete: false,
				}

				serverMessage := &LiveServerMessage{
					ServerContent: serverContent,
				}

				// Log for debugging
				if isFirstChunk {
					log.Printf("Starting streaming response for user %s", lc.userID)
					isFirstChunk = false
				}

				if err := lc.conn.WriteJSON(serverMessage); err != nil {
					log.Printf("Error sending streaming chunk: %v", err)
					return err
				}
			}

			// Send turn complete when done
			if response.Done {
				log.Printf("Streaming completed for user %s, total content length: %d", lc.userID, accumulatedContent.Len())

				completeMessage := &LiveServerMessage{
					ServerContent: &ServerContent{
						TurnComplete: true,
					},
				}
				if err := lc.conn.WriteJSON(completeMessage); err != nil {
					return err
				}
				return nil
			}
		}
	}
}

// Handle realtime input (audio/video)
func (lc *LiveConnection) handleRealtimeInput(input *BidiGenerateContentRealtimeInput) error {
	// TODO: Implement realtime audio/video processing
	log.Printf("Received realtime input with %d media chunks", len(input.MediaChunks))
	return nil
}

// Handle tool response
func (lc *LiveConnection) handleToolResponse(response *BidiGenerateContentToolResponse) error {
	// TODO: Implement tool response handling
	log.Printf("Received tool response with %d function responses", len(response.FunctionResponses))
	return nil
}

// Send error message
func (lc *LiveConnection) sendError(message string) {
	errorMsg := map[string]interface{}{
		"error": map[string]string{
			"message": message,
		},
	}

	if err := lc.conn.WriteJSON(errorMsg); err != nil {
		log.Printf("Failed to send error message: %v", err)
	}
}
