package gorm

import (
	"log"
	"time"

	"github.com/animus-server/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// LLMProviderRepository implements db.LLMProviderRepository with GORM
type LLMProviderRepository struct{}

// NewLLMProviderRepository creates a new GORM LLM provider repository
func NewLLMProviderRepository() *LLMProviderRepository {
	return &LLMProviderRepository{}
}

// GetLLMProvider gets an LLM provider by ID
func (r *LLMProviderRepository) GetLLMProvider(id string, userID string) (*models.LLMProvider, error) {
	db := DB()
	if db == nil {
		return nil, ErrDatabaseNotInitialized
	}

	var provider GormLLMProvider
	result := db.Where("id = ? AND user_id = ?", id, userID).First(&provider)
	if result.Error != nil {
		log.Printf("Error getting LLM provider: %v", result.Error)
		return nil, result.Error
	}

	return ToModelLLMProvider(&provider), nil
}

// GetUserLLMProviders gets all LLM providers for a user
func (r *LLMProviderRepository) GetUserLLMProviders(userID string) ([]models.LLMProvider, error) {
	db := DB()
	if db == nil {
		return nil, ErrDatabaseNotInitialized
	}

	var providers []GormLLMProvider
	result := db.Where("user_id = ?", userID).Find(&providers)
	if result.Error != nil {
		log.Printf("Error getting user LLM providers: %v", result.Error)
		return nil, result.Error
	}

	// Convert to model types
	modelProviders := make([]models.LLMProvider, len(providers))
	for i, provider := range providers {
		modelProviders[i] = *ToModelLLMProvider(&provider)
	}

	return modelProviders, nil
}

// CreateLLMProvider creates a new LLM provider
func (r *LLMProviderRepository) CreateLLMProvider(provider *models.LLMProvider) error {
	db := DB()
	if db == nil {
		return ErrDatabaseNotInitialized
	}

	// Set default values
	if provider.ID == "" {
		provider.ID = uuid.New().String()
	}
	if provider.CreatedAt.IsZero() {
		provider.CreatedAt = time.Now()
	}
	if provider.UpdatedAt.IsZero() {
		provider.UpdatedAt = time.Now()
	}

	// If this provider is set as default, unset any existing default provider
	if provider.IsDefault {
		err := r.clearDefaultProvider(db, provider.UserID)
		if err != nil {
			return err
		}
	}

	gormProvider := ToGormLLMProvider(provider)
	result := db.Create(gormProvider)
	if result.Error != nil {
		log.Printf("Error creating LLM provider: %v", result.Error)
		return result.Error
	}

	return nil
}

// UpdateLLMProvider updates an existing LLM provider
func (r *LLMProviderRepository) UpdateLLMProvider(provider *models.LLMProvider) error {
	db := DB()
	if db == nil {
		return ErrDatabaseNotInitialized
	}

	// Update timestamp
	provider.UpdatedAt = time.Now()

	// If this provider is set as default, unset any existing default provider
	if provider.IsDefault {
		err := r.clearDefaultProvider(db, provider.UserID)
		if err != nil {
			return err
		}
	}

	gormProvider := ToGormLLMProvider(provider)
	result := db.Where("id = ? AND user_id = ?", provider.ID, provider.UserID).Updates(gormProvider)
	if result.Error != nil {
		log.Printf("Error updating LLM provider: %v", result.Error)
		return result.Error
	}

	return nil
}

// DeleteLLMProvider deletes an LLM provider
func (r *LLMProviderRepository) DeleteLLMProvider(id string, userID string) error {
	db := DB()
	if db == nil {
		return ErrDatabaseNotInitialized
	}

	// Check if provider exists and is the default one
	var provider GormLLMProvider
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&provider).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Provider doesn't exist, so we're done
			return nil
		}
		log.Printf("Error checking LLM provider before deletion: %v", err)
		return err
	}

	result := db.Where("id = ? AND user_id = ?", id, userID).Delete(&GormLLMProvider{})
	if result.Error != nil {
		log.Printf("Error deleting LLM provider: %v", result.Error)
		return result.Error
	}

	return nil
}

// SetDefaultProvider sets a provider as the default for a user
func (r *LLMProviderRepository) SetDefaultProvider(id string, userID string) error {
	db := DB()
	if db == nil {
		return ErrDatabaseNotInitialized
	}

	// Start transaction
	err := db.Transaction(func(tx *gorm.DB) error {
		// First, clear any existing default provider
		if err := r.clearDefaultProvider(tx, userID); err != nil {
			return err
		}

		// Set the new default provider
		result := tx.Model(&GormLLMProvider{}).
			Where("id = ? AND user_id = ?", id, userID).
			Update("is_default", true)

		if result.Error != nil {
			log.Printf("Error setting default LLM provider: %v", result.Error)
			return result.Error
		}

		return nil
	})

	return err
}

// GetDefaultProvider gets the default LLM provider for a user
func (r *LLMProviderRepository) GetDefaultProvider(userID string) (*models.LLMProvider, error) {
	db := DB()
	if db == nil {
		return nil, ErrDatabaseNotInitialized
	}

	var provider GormLLMProvider
	result := db.Where("user_id = ? AND is_default = ?", userID, true).First(&provider)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// No default provider found
			return nil, nil
		}
		log.Printf("Error getting default LLM provider: %v", result.Error)
		return nil, result.Error
	}

	return ToModelLLMProvider(&provider), nil
}

// Helper method to clear default provider within a transaction
func (r *LLMProviderRepository) clearDefaultProvider(db *gorm.DB, userID string) error {
	result := db.Model(&GormLLMProvider{}).
		Where("user_id = ? AND is_default = ?", userID, true).
		Update("is_default", false)

	if result.Error != nil {
		log.Printf("Error clearing default LLM provider: %v", result.Error)
		return result.Error
	}

	return nil
}
