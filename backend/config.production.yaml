# Production Configuration for Animus Web Application
# This configuration is used in production environment

server:
  port: 8080

supabase:
  url: https://xsibkeuutfrmpdphcfwr.supabase.co
  key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.I3tPrkDpMWeMoi3K26e6TFjDwCgLv1gg-atHI8LOBnU

logto:
  endpoint: https://auth.animus.run/
  appId: kxg8wk21334uj1jupr4wh
  appSecret: yM5JdCoLYtHpn2i4nLKqeIStqMOuSKek
  # Production redirect URIs
  redirectUri: https://animus.run/api/v1/login/callback
  postLogoutRedirectUri: https://animus.run/

database:
  # Production database configuration
  type: sqlite
  # Production SQLite database path
  dsn: "file:./data/data.db?cache=shared"
  # Use GORM for database operations
  useGorm: true

# LLM Provider Configuration for Production
llmProvider:
  # Initial credits given to new users (production)
  initialCredits: 5000
  systemProviders:
    - id: "sys-deepseek-r1-0528"
      name: "deepseek-r1-0528 (System)"
      alias: "sys-deepseek-r1-0528"
      type: "openai"
      apiKey: "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"
      baseUrl: "https://openrouter.ai/api/v1"
      model: "deepseek/deepseek-r1-0528:free"
      systemPrompt: ""
      isDefault: false
      temperature: 0.7
      maxTokens: 4000
      topP: 1.0
      frequencyPenalty: 0.0
      presencePenalty: 0.0
      enabled: true

    - id: "sys-gpt4o"
      name: "GPT-4o (System)"
      alias: "sys-gpt4o"
      type: "openai"
      apiKey: "system_managed"
      baseUrl: "https://api.openai.com/v1"
      model: "gpt-4o"
      systemPrompt: ""
      isDefault: false
      temperature: 0.7
      maxTokens: 4000
      topP: 1.0
      frequencyPenalty: 0.0
      presencePenalty: 0.0
      enabled: true

    - id: "sys-claude3"
      name: "Claude 3 Sonnet (System)"
      alias: "sys-claude3"
      type: "anthropic"
      apiKey: "system_managed"
      baseUrl: "https://api.anthropic.com"
      model: "claude-3-sonnet-20240229"
      systemPrompt: ""
      isDefault: false
      temperature: 0.7
      maxTokens: 4000
      topP: 1.0
      frequencyPenalty: 0.0
      presencePenalty: 0.0
      enabled: true

    - id: "sys-gemini"
      name: "Gemini Pro (System)"
      alias: "sys-gemini"
      type: "google"
      apiKey: "system_managed"
      baseUrl: "https://generativelanguage.googleapis.com/v1beta"
      model: "gemini-pro"
      systemPrompt: ""
      isDefault: false
      temperature: 0.7
      maxTokens: 4000
      topP: 1.0
      frequencyPenalty: 0.0
      presencePenalty: 0.0
      enabled: true

# Production-specific settings
logging:
  level: info
  format: json
  output: file
  file: ./logs/animus.log

security:
  # Enable HTTPS cookies in production
  secureCookies: true
  # Enable CORS for production domains
  corsOrigins:
    - https://animus.run
    - https://www.animus.run
  # Rate limiting settings
  rateLimit:
    enabled: true
    requestsPerMinute: 60
    burstSize: 10

# Performance settings
performance:
  # Enable gzip compression
  gzipEnabled: true
  # Cache settings
  cacheEnabled: true
  cacheTTL: 3600 # 1 hour

# Monitoring and health checks
monitoring:
  healthCheckEnabled: true
  metricsEnabled: true
  # Prometheus metrics endpoint
  metricsPath: /metrics
