@echo off
REM Production startup script for Animus Web Application

echo Starting Animus Web Application in Production Mode...

REM Set environment variables for production
set APP_ENV=production
set GIN_MODE=release
set LOG_LEVEL=info
set LOG_FORMAT=json
set LOG_OUTPUT=file
set SECURE_COOKIES=true
set GZIP_ENABLED=true
set CACHE_ENABLED=true
set METRICS_ENABLED=true

REM Create necessary directories
if not exist "logs" mkdir logs
if not exist "data" mkdir data

echo Environment: %APP_ENV%
echo Port: %PORT%
echo Log Level: %LOG_LEVEL%

REM Build and run the application
echo Building application for production...
set CGO_ENABLED=1
go build -a -ldflags "-w -s" -o animus-server.exe ./cmd/server

if %ERRORLEVEL% EQU 0 (
    echo Build successful. Starting server...
    animus-server.exe
) else (
    echo Build failed!
    exit /b 1
)
