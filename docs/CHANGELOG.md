# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **Real XunhuPay Integration**: Integrated real XunhuPay payment system using SDK-based approach
  - **SDK Integration**: Replaced custom HTTP implementation with XunhuPay SDK pattern for better reliability
  - **HuPi Client**: Added `HuPiClient` struct with `Execute` and `Sign` methods following official SDK design
  - **Enhanced Payment Creation**: Updated `CreatePayment` method to use SDK's parameter structure and API endpoints
  - **Improved Query System**: Modified `QueryPayment` to use SDK's query endpoint with proper parameter mapping
  - **Signature Verification**: Enhanced `VerifyNotification` to use SDK's signature generation for secure callback verification
  - **Configuration Example**: Added `xunhupay_example.yaml` with comprehensive configuration guide and usage instructions
  - **Testing Suite**: Created comprehensive test suite for XunhuPay provider functionality
  - **Documentation**: Added detailed setup instructions and configuration examples for production deployment
- **Configurable System LLM Providers**: Moved built-in system LLM providers from hardcoded values to configuration files
  - **Configuration Structure**: Added `LLMProviderConfig` and `SystemLLMProvider` structs to config package
  - **Environment-Specific Configs**: Added LLM provider configurations to `config.yaml`, `config.development.yaml`, and `config.production.yaml`
  - **Dynamic Loading**: Modified `GetSystemProviders` endpoint to load providers from configuration instead of hardcoded values
  - **Enabled/Disabled Control**: Added `enabled` field to allow administrators to enable/disable specific system providers
  - **Flexible Configuration**: System administrators can now modify provider settings (API keys, models, parameters) through configuration files
  - **Environment Separation**: Different environments can have different system provider configurations

### Fixed
- **Live API Streaming Message Optimization**: Enhanced WebSocket streaming to display messages in single message box
  - **Backend Streaming Logic**: Improved `streamResponse` method to properly handle incremental message updates
  - **Message Accumulation**: Added content tracking to ensure proper streaming session management
  - **Frontend Message Handling**: Enhanced `handleLiveAPIMessage` to correctly accumulate streaming text in same message box
  - **State Management**: Improved streaming message ID management to prevent multiple message boxes
  - **Error Handling**: Added comprehensive error handling for streaming failures and WebSocket disconnections
  - **Debug Logging**: Enhanced logging for streaming sessions to facilitate troubleshooting
  - **System Provider Fallback**: Fixed Live API to use system providers when no user providers are configured
  - **Provider Resolution**: Added `getAvailableProvider` method to seamlessly fall back to system providers
  - **Configuration Integration**: Live API now properly loads system providers from configuration files
- **Chat API System Provider Integration**: Fixed "Invalid system LLM provider" error in chat functionality
  - **ConversationManager Update**: Updated `getSystemProviders` method to load from configuration instead of hardcoded values
  - **Unified Provider Loading**: Both `/api/v1/llm-providers/system` and chat functionality now use the same configuration source
  - **API Key Resolution**: Enhanced API key handling to support both configured keys and environment variable fallbacks
  - **Provider Filtering**: Only enabled providers from configuration are loaded and available for chat
- **Automatic Initial Credits System**: Implemented automatic credit allocation for new users
  - **Configurable Initial Credits**: Added `initialCredits` setting to LLM provider configuration
  - **Environment-Specific Credits**: Development (10000), Production (5000) initial credits
  - **Automatic Credit Creation**: New users automatically receive initial credits when first accessing the system
  - **Transaction Logging**: Initial credit allocation is properly logged as "Welcome bonus" transaction
  - **Configuration Integration**: Initial credit amount is configurable through YAML files
- **Mock Payment Provider for Testing**: Implemented a complete mock payment system for development and testing
  - **Mock Payment Provider**: Added `mock.MockPaymentProvider` with configurable success rates and delays
  - **Configuration Support**: Added `MockPayConfig` to payment configuration with success rate, delay, and auto-completion settings
  - **Payment Simulation API**: Added `/api/v1/payments/mock/:order_id/simulate` endpoint for manual payment completion testing
  - **Provider Registration**: Mock payment provider is automatically registered when enabled in configuration
  - **Testing Features**: Supports immediate payment completion, failure simulation, and configurable processing delays
  - **Default Configuration**: Mock payment is enabled by default with 100% success rate for easy testing
- **Multi-Provider Authentication System**: Integrated Casdoor authentication alongside existing Logto system
  - **Unified Authentication Interface**: Created `AuthProvider` interface for consistent authentication operations
  - **Authentication Manager**: Centralized manager supporting multiple authentication providers
  - **Casdoor Integration**: Complete Casdoor authentication implementation using `github.com/casdoor/casdoor-go-sdk`
  - **Provider Switching**: Runtime switching between Logto and Casdoor providers via `AUTH_PROVIDER` environment variable
  - **Configuration Support**: Added Casdoor configuration to config files with environment variable overrides
  - **Extended API**: Casdoor-specific methods for user management (GetUser, GetUsers, AddUser, UpdateUser, DeleteUser)
  - **Session Management**: Unified session handling across different authentication providers
  - **Documentation**: Comprehensive authentication system documentation with usage examples
- **Official Website Integration**: Integrated existing static website from `website/static` directory
  - **Root Path Routing**: Golang backend now serves static website at root path (`/`)
  - **Static File Serving**: Configured routes for CSS, JS, and image assets
  - **Console Integration**: Modified login buttons to redirect to `/console` for backend control panel
  - **Path Auto-Detection**: Backend automatically detects website path from project root or backend directory
- **Enhanced Navigation System**: Complete navigation overhaul with improved UX
  - **Dual Login Options**: Added separate "登录/Login" and "控制台/Console" buttons
  - **Smooth Scrolling**: Implemented smooth scroll navigation for anchor links
  - **Login State Management**: Dynamic button text based on authentication status
  - **Responsive Design**: Optimized navigation for all device sizes


- **Agent Pod Management System**: Complete AI Agent Pod management functionality
  - Cloud Pod creation and management with container images and resource limits
  - Self-registered Pod support for running agents on local machines
  - Pod lifecycle management (create, update, delete, restart)
  - Real-time status monitoring and statistics dashboard
  - Workspace file browser for Pod workspace exploration
  - Pod heartbeat system for self-registered agents
  - Resource monitoring (CPU, memory requests/limits)
  - Pod labeling and namespace support
  - Multi-language support (English and Chinese)

#### Backend Components
- `AgentPod` data model with complete GORM implementation
- `AgentPodRepository` interface and GORM implementation
- `AgentPodHandler` with comprehensive HTTP endpoints
- Database auto-migration for AgentPod tables
- API endpoints for CRUD operations, statistics, and workspace browsing
- Self-registration API for external agents
- Heartbeat monitoring for agent connectivity

#### Frontend Components
- `AgentPods.tsx` - Complete Pod management interface
- Cloud Pod creation form with resource configuration
- Self-registered Pod setup guide with download instructions
- Real-time Pod status monitoring with auto-refresh
- Statistics cards showing Pod distribution and status
- Workspace file browser with directory navigation
- Responsive design for mobile and desktop
- Internationalization support (zh/en)

#### Features
- **Pod Types**: Support for both cloud-managed and self-registered Pods
- **Resource Management**: CPU and memory requests/limits configuration
- **Status Monitoring**: Real-time Pod status with visual indicators
- **Workspace Access**: File system browser for Pod workspaces
- **Statistics Dashboard**: Overview of Pod counts and status distribution
- **Self-Registration**: Easy setup for external agent registration
- **Responsive UI**: Mobile-friendly interface with adaptive layout
- **Security**: User-based Pod isolation and access control

### Updated
- **Website JavaScript Integration**: Modified `website/static/js/main.js` for backend integration
  - Updated login status check to use `/api/v1/me` endpoint (fixed 404 error)
  - Changed login button redirects from `/dashboard` to `/console`
  - Updated button text to show "控制台/Console" instead of "Dashboard"
  - Added dynamic navbar and footer generation functions
- **HTML Asset Paths**: Fixed asset references in `website/static/index.html`
  - Changed CSS path from `/static/css/style.css` to `/css/style.css`
  - Changed JS path from `/static/js/main.js` to `/js/main.js`
  - Removed reference to non-existent `templates.js` file
- **Configuration Management**: Moved Logto configuration from hardcoded values to centralized config system
  - Backend `auth_handlers.go` now reads from config files instead of hardcoded values
  - Frontend authentication flow simplified to use backend endpoints only
  - Removed frontend Logto configuration dependencies
  - Updated documentation to reflect new architecture
- **WebSocket URL**: Dynamic construction based on current host and protocol (HTTP/HTTPS → WS/WSS)
- **Authentication Flow**: Complete backend control of Logto authentication
  - Frontend login/logout now redirects to backend endpoints
  - Removed hardcoded Logto configuration from frontend
  - Centralized configuration in `backend/config.yaml`
- Navigation bar with Agent Pod menu item
- Database initialization to include AgentPod models
- Repository manager to include AgentPod repository
- Server manager with complete routing configuration
- Translation files with comprehensive Agent Pod terminology

### Technical Details
- GORM models with proper relationships and constraints
- JSON serialization for Pod labels and configuration
- RESTful API design following project conventions
- Type-safe TypeScript interfaces throughout
- Consistent error handling and user feedback
- Proper validation for Pod creation and updates

### Fixed
- **Navigation Bar Simplification**: Streamlined navbar design and functionality
  - **Removed Duplicate Login Button**: Eliminated redundant "登录/Login" button from navigation
  - **Unified Console Access**: Single "进入控制台/Enter Console" button for all access needs
  - **Consistent Styling**: Console link now matches other navigation links' color scheme
    - Uses `var(--text-primary)` color instead of special button styling
    - Hover effect changes to `var(--primary-color)` like other nav links
    - Removed border and background styling for cleaner appearance
  - **Dynamic Text Updates**: Button text changes based on login status
    - Not logged in: "进入控制台/Enter Console" → `/console/login`
    - Logged in: "控制台/Console" → `/console`
- **Contact Section Display Issues**: Resolved missing contact cards and button visibility
  - **Fixed Language Content Display**: Enhanced CSS with `!important` rules for `.lang-content` visibility
  - **Resolved JavaScript Conflicts**: Removed duplicate language manager initialization
  - **Enhanced Button Styling**: Added specific CSS rules for login button text color visibility
  - **Fixed Contact Cards**: Ensured "邮箱联系", "在线客服", and "社交媒体" cards display correctly
  - **Cross-page Navigation**: Fixed "联系我们/Contact" link functionality from About page
    - Changed anchor links to absolute paths (`/#contact`) for cross-page navigation
    - Added automatic scroll-to-anchor functionality for page loads with hash URLs
    - Enhanced smooth scrolling logic to handle both same-page and cross-page navigation
- **Final Website Review & Optimization**: Complete overhaul based on user feedback
  - **Removed Unnecessary CTAs**: Deleted "探索核心特性/Explore Core Features" and subscription buttons
  - **Enhanced Navigation Structure**: Restructured navbar with logical button placement
    - Added "登录/Login" button next to "联系我们/Contact" for better UX flow
    - Maintained "控制台/Console" button as primary action button
    - Implemented proper button hierarchy and visual distinction
  - **Fixed Contact Navigation**: Resolved non-functional "联系我们/Contact" link
    - Added smooth scrolling functionality for all anchor links (#features, #use-cases, #contact)
    - Implemented proper scroll behavior with `scrollIntoView` API
    - Enhanced user experience with seamless page navigation
  - **Optimized Button Styling**: Added distinct styles for different button types
    - `.nav-login-btn`: Outline style for secondary login action
    - `.login-btn`: Filled style for primary console action
    - Consistent hover effects and transitions across all buttons
- **Complete Website Overhaul**: Comprehensive review and optimization of the entire official website
  - **Navigation-Content Alignment**: Fixed mismatch between navbar links and page content
    - Added missing `#use-cases` section to homepage with proper ID anchor
    - Added missing `#contact` section to homepage with contact information
    - Ensured all navbar links (`#features`, `#use-cases`, `#contact`) point to existing content
  - **About Page Routing**: Fixed `/about` redirect issue
    - Added proper backend route: `router.StaticFile("/about", websitePath+"/about.html")`
    - Fixed asset paths in about.html (CSS/JS references)
    - Ensured `/about` serves the correct static HTML page instead of redirecting to console
  - **Login Button Visibility**: Resolved missing login button issue
    - Restructured navbar HTML to properly nest language-specific content
    - Fixed CSS display logic for multilingual elements
    - Enhanced login button styling with gradient background and animations
    - Updated button text: "进入控制台/Enter Console"
- **Language Switching System**: Complete overhaul of bilingual functionality
  - **Fixed Toggle Logic**: Implemented proper `toggleLanguage()` and `switchLanguage()` methods
  - **Content Display**: Fixed `updateContent()` method to properly show/hide language-specific elements
  - **Visual Indicators**: Added Chinese/English flag icons (🇨🇳/🇺🇸) for better UX
  - **Navbar Structure**: Restructured navigation to support proper bilingual display
  - **CSS Integration**: Aligned JavaScript logic with existing CSS classes (.lang-content, .lang-inline, .lang-flex)
- **Backend Route Configuration**: Enhanced static file serving
  - Added explicit route for `/about` page
  - Improved path detection for website assets
  - Maintained separation between website routes and console routes
- **API Endpoint Alignment**: Fixed authentication check
  - Changed from `/api/v1/user` to `/api/v1/me` endpoint
  - Aligned frontend calls with backend API structure
- **Asset Path Consistency**: Standardized all asset references
  - Updated all HTML files to use correct paths (`/css/`, `/js/`, `/images/`)
  - Removed references to non-existent files
  - Ensured consistent asset loading across all pages

---

## Previous versions
*Previous changelog entries would be listed here in chronological order*

## [2024-12-26] - Chat UI Improvements & System LLM Providers

### Added
- System built-in LLM providers with predefined configurations
- New API endpoint `/api/v1/llm-providers/system` to fetch system providers
- System providers appear with "sys-" prefix in alias names
- Built-in providers for GPT-4o, Claude 3 Sonnet, and Gemini Pro

### Changed
- Chat page sidebar now collapses by default for better screen space utilization
- Optimized fold button and LLM provider dropdown styling:
  - Removed borders for cleaner look
  - Reduced font sizes for more compact appearance
  - Applied light color scheme with subtle shadows
  - Improved hover effects and transitions
- LLM config table now prioritizes system providers at the top
- Added provider type column to distinguish between system and user providers
- **Chat Interface Style Updates**:
  - Chat control bar background changed to light gray (#f8f9fa) matching message area
  - Model selection dropdown button background changed to gray (#e8eaed) for better visual hierarchy
  - Dropdown button now auto-adapts to content width for cleaner appearance
  - Enhanced hover effects with darker gray (#dadce0) for better interactivity
  - Improved visual consistency across chat interface components
  - **Fixed sidebar default state**: Left conversation sidebar now properly defaults to collapsed on all screen sizes
  - **Removed control bar border**: Eliminated bottom border from chat control bar for cleaner appearance
  - **Adjusted dropdown position**: Model selection dropdown moved down slightly for better visual balance
  - **Enhanced input functionality**: Chat input now supports multi-line text with Shift+Enter for line breaks
  - **Updated user message styling**: User messages now display with light gray background (#f5f5f5) for better visual consistency
  - **MCP terminology update**: Changed all "MCP 服务器/MCP Servers" references to "MCP 服务/MCP Services" for clearer branding
  - **Fixed MCP Services data loading**: Improved API response handling and added proper authentication headers to prevent display issues

### Fixed
- Provider dropdown styling improved with proper spacing and colors
- Better visual hierarchy in provider selection interface
- Model selection dropdown now properly centers vertically in control bar

### UI/UX Improvements
- Sidebar toggle button styling refreshed with transparency and subtle colors
- Provider dropdown menu redesigned with rounded corners and modern shadows
- Tag styling updated for better visual distinction between provider types
- Disabled editing/deletion for system providers to maintain integrity
- **Enhanced Visual Consistency**: All control elements now use consistent color scheme
- **Better Content Adaptation**: Model selection button adjusts to content size automatically 